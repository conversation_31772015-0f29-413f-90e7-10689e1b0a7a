# Current Virtual Card Creation Flow Analysis

## Overview
The current virtual card creation system uses a workflow engine with state transitions to handle the complete card creation lifecycle. The process involves multiple services and follows a specific sequence of states.

## Current Flow Architecture

### 1. API Entry Point
**File**: `digicard-core/handlers/digicardcore_create_card.handler.go`

**Flow**:
```
POST /v1/card → CreateCard Handler
├── Validate user ID (X-Grab-Id-Userid header)
├── Validate request parameters (idempotencyKey, accountID, cardDesignId, type, displayName)
├── Check user whitelist
├── Verify account via AccountVerifyClient
├── Apply Redis lock (CreateCardLock:{userID}) for 3 seconds
├── Persist cards terms acceptance
├── Route based on card type:
│   ├── Physical Card → PhysicalCardOrderClient.PhysicalCardOrder()
│   └── Virtual Card → CreateCardWorkflow.ExecuteCreateCardWorkflow()
└── Publish card creation audit log
```

### 2. Virtual Card Creation Workflow
**File**: `digicard-core/logic/card/create/create_card.go`

**Main Method**: `ExecuteCreateCardWorkflow()`

**Flow**:
```
ExecuteCreateCardWorkflow()
├── Extract userID from context
├── Create ExecutionData with initial state (stInit)
├── Fetch card design by cardDesignId
├── Execute virtual card creation:
│   ├── Check card limits (max 2 active cards, max 99 total)
│   ├── Validate card design availability (stock count ≥ 10)
│   ├── Initialize workflow engine execution
│   ├── Handle idempotency (return existing if already exists)
│   └── Execute workflow with event: evPersistAndProcessCardCreation
└── Convert execution data to API response
```

### 3. Workflow State Machine
**File**: `digicard-core/logic/card/create/workflow.go`

**States**:
- `stInit` (0) - Initial state
- `stCardPersisted` (100) - Card saved to database
- `stVirtualRiskChecked` (210) - Risk check completed
- `stVirtualIssued` (201) - Card issued via Euronet
- `stPushNotified` (215) - Push notification sent
- `stEmailNotified` (220) - Email notification sent
- `stFailed` (500) - Failed state

**State Transitions**:
```
stInit → [evPersistAndProcessCardCreation] → persistCard() → stCardPersisted
stCardPersisted → [evNoNeed] → riskCheck() → stVirtualRiskChecked
stVirtualRiskChecked → [evNoNeed] → issueVirtualCard() → stVirtualIssued
stVirtualIssued → [evNoNeed] → sendPush() → stPushNotified
stPushNotified → [evNoNeed] → sendEmail() → stEmailNotified
```

### 4. Detailed State Implementations

#### 4.1 persistCard() - State: stCardPersisted
**File**: `digicard-core/logic/card/create/persist.go`

**Actions**:
- Generate new UUID for cardID
- Create Card record in database with:
  - Status: "PROCESSING"
  - Provider: "MASTERCARD"
  - Vendor: "EURONET"
  - Toggle settings (CNP enabled/disabled)
  - PostScript data (delivery address, activation code)
- Save to `card` table

#### 4.2 riskCheck() - State: stVirtualRiskChecked
**File**: `digicard-core/logic/card/create/risk_check.go`

**Actions**:
- Call RiskServiceClient.ValidatePartnerTxn()
- Handle risk check responses:
  - ALLOW: Continue to next state
  - DENY: Fail the workflow
  - MAYBE: Continue with warning
- Bypass on 5xx errors from risk service

#### 4.3 issueVirtualCard() - State: stVirtualIssued
**File**: `digicard-core/logic/card/create/card_issuance.go`

**Actions**:
- Call EuronetAdapterClient.CardIssuance() with:
  - CardID, CardIssuanceType: "VIRTUAL", IdempotencyKey, DisplayName
- Process Euronet Adapter response
- Update card in database with:
  - Vendor card details (vendorCardID, cardSequenceNo)
  - Card numbers (head/tail)
  - Expiry date, status
- Publish card activity to Kafka (background)

#### 4.4 sendPush() - State: stPushNotified
**File**: `digicard-core/logic/card/create/notify.go`

**Actions**:
- Build push notification request with tail card number
- Call PushNotifier.Notify() via Pigeon service
- Template: "virtual_issued_push_notification"

#### 4.5 sendEmail() - State: stEmailNotified
**File**: `digicard-core/logic/card/create/notify.go`

**Actions**:
- Resolve username via CustomerMasterClient.GetCustomer()
- Build email notification request
- Call EmailNotifier.Notify() via Pigeon service
- Template: "virtual_issued_email_notification"

### 5. Euronet Adapter Workflow
**File**: `euronet-adapter/logic/card/issuance/issuance.go`

**Flow**:
```
CardIssuance API → ExecuteCardIssuanceWorkflow()
├── Create ExecutionData with CardIssuanceRequest
├── Initialize workflow: "cardIssuanceWorkflow"
├── Execute with event: evPersistRequest
└── State transitions:
    stInit → persistRequest() → stRequestPersisted
    stRequestPersisted → invokeEuronetIssuance() → stVirtualCardIssued
    stVirtualCardIssued → persistUserCardMapping() → stSuccess
```

#### 5.1 persistRequest() - Euronet Adapter
**File**: `euronet-adapter/logic/card/issuance/persist_request.go`

**Actions**:
- Generate Euronet customer ID
- Create CardActivity record
- Construct EN issuance request payload
- Save to `card_activity` table

#### 5.2 invokeEuronetIssuance() - Euronet Adapter
**File**: `euronet-adapter/logic/card/issuance/invoke_euronet.go`

**Actions**:
- Call external Euronet CMS API
- Handle response and map to internal format
- Update CardActivity with response

#### 5.3 persistUserCardMapping() - Euronet Adapter
**File**: `euronet-adapter/logic/card/issuance/persist_mapping.go`

**Actions**:
- Create user-card mapping record
- Save to `user_card_mapping` table

## Files Affected by CCC Implementation

### 1. Core API & Handler Files
```
digicard-core/api/digicard_core.proto                    [MODIFY] - Add CCC endpoints
digicard-core/api/digicard_core.api.go                   [GENERATED] - Auto-generated from proto
digicard-core/handlers/digicardcore.routes.go            [MODIFY] - Add CCC routes
digicard-core/handlers/digicardcore.service.go           [MODIFY] - Add CCC dependencies
digicard-core/handlers/digicardcore_create_virtual_card_ccc.handler.go [NEW] - CCC handler
```

### 2. Workflow & Logic Files
```
digicard-core/logic/card/create/create_card.go           [MODIFY] - Add CCC workflow entry
digicard-core/logic/card/create/workflow.go              [MODIFY] - Register CCC workflow
digicard-core/logic/card/create/ccc_workflow.go          [NEW] - CCC-specific workflow
digicard-core/logic/card/create/rloc_client.go           [NEW] - Customer-master integration
digicard-core/logic/card/create/limit_setter.go          [NEW] - Default limit setting
```

### 3. Storage & Database Files
```
common/constant/product_variant.go                       [NEW] - CCC product variant constants
digicard-core/logic/card/create/ccc_helper.go            [NEW] - CCC card helper functions
```

### 4. Configuration Files
```
digicard-core/config_files/service-conf.json             [MODIFY] - Add CCC config
digicard-core/server/config/config.go                    [MODIFY] - Add CCC config structs
digicard-core/server/serve.go                            [MODIFY] - Wire CCC dependencies
```

### 5. Common & Utility Files
```
common/constant/card_status.go                           [MODIFY] - Add CCC statuses
common/constant/customer_type.go                         [NEW] - Customer type constants
digicard-core/logic/helper.go                            [MODIFY] - Add CCC helper functions
```

### 6. External Integration Files
```
digicard-core/logic/address/mailing/mailing_client.go    [REFERENCE] - Customer-master pattern
digicard-core/logic/terms/terms.go                       [REFERENCE] - Customer-master pattern
```

### 7. Notification Files
```
digicard-core/logic/notification/push_notifier.go        [MODIFY] - Add CCC templates
digicard-core/logic/notification/email_notifier.go       [MODIFY] - Add CCC templates
digicard-core/logic/notification/const.go                [MODIFY] - Add CCC notification types
```

### 8. Test Files
```
digicard-core/test/api/create_card_ccc_api_test.go        [NEW] - CCC API tests
digicard-core/logic/card/create/ccc_workflow_test.go     [NEW] - CCC workflow tests
digicard-core/handlers/digicardcore_create_virtual_card_ccc.handler_test.go [NEW] - Handler tests
```

## Key Integration Points

### 1. Customer-Master Service
- **Purpose**: Fetch RLOC account information
- **Endpoint**: `/v2/customers/{customerID}`
- **Pattern**: Follow existing `mailing_client.go` implementation
- **Data**: Account ID, credit limits, account status

### 2. Transaction Limit Service
- **Purpose**: Set default transaction limits
- **Pattern**: Follow existing `get_card_limits.handler.go`
- **Limits**: Online, Contactless, ATM, Pin&Pay

### 3. Notification Service (Pigeon)
- **Purpose**: Send success/failure notifications
- **Pattern**: Follow existing `push_notifier.go` and `email_notifier.go`
- **Templates**: CCC-specific notification templates

### 4. Workflow Engine
- **Purpose**: State management and retry logic
- **Pattern**: Follow existing `workflow.go` pattern
- **States**: Custom CCC states with retry capabilities

## Simplified CCC Implementation Approach

### Leveraging Existing Table Structure
Instead of creating new tables, the CCC implementation will use:

- **`product_variant`** column to distinguish CCC cards ("CCC_VIRTUAL_CARD")
- **`account_id`** column to store RLOC account ID
- **`reference_id`** column for idempotency tracking
- **`status`** column with existing standard statuses ("PROCESSING", "ACTIVE", "FAILED")
- **`post_script`** JSON column for retry metadata

### Benefits of This Approach
1. **No Database Migration Required** - Uses existing schema completely
2. **No New Status Constants** - Reuses existing PROCESSING/ACTIVE/FAILED statuses
3. **Consistent with Current Architecture** - Follows established patterns
4. **Simplified Data Model** - Single source of truth for card data
5. **Easier Maintenance** - Fewer constants and tables to manage
6. **Better Performance** - No additional joins or status mapping required

This analysis shows that the CCC implementation will primarily extend the existing virtual card creation flow with additional validation, RLOC integration, enhanced retry logic, and status tracking while maintaining compatibility with the current architecture and leveraging the existing database schema efficiently.
