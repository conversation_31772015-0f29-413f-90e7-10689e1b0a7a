# Implementation Plan: Modular Card Creation Workflow Enhancement

## Overview
Enhance the existing `POST /v1/card` endpoint to support CCC virtual card creation using a modular, strategy-based architecture that eliminates scattered conditional logic and promotes maintainability.

## Architecture Strategy
1. **Strategy Pattern**: Implement product-specific behaviors through strategy interfaces
2. **Configuration-Driven**: Leverage existing `ProductConfigs` map for product settings
3. **Factory Pattern**: Create appropriate strategies based on product variant
4. **Dependency Injection**: Inject product strategies into workflow components
5. **Backward Compatibility**: Ensure existing debit card flow remains unchanged

## Core Architecture Components

### Strategy Interface Design
```go
// ProductStrategy defines the interface for product-specific behaviors
type ProductStrategy interface {
    ValidateAccount(ctx context.Context, accountID string) error
    GetRiskCheckParams(ctx context.Context, card *storage.Card) *RiskCheckParams
    GetDefaultTransactionLimits() map[string]int64
    GetCardIssuanceParams(ctx context.Context, card *storage.Card) *CardIssuanceParams
    GetCardLimits() *CardLimits
}
```

## Implementation Steps

### Step 1: Create Strategy Interface

**File**: `digicard-core/logic/card/create/strategy/product_strategy.go`
```go
package strategy

import (
    "context"
    
    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
    riskAPI "gitlab.myteksi.net/dakota/client-sdk/risk-service"
    euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
)

// ProductStrategy defines the interface for product-specific card creation behaviors
type ProductStrategy interface {
    // ValidateAccount performs product-specific account validation
    ValidateAccount(ctx context.Context, accountID string) error
    
    // GetRiskCheckParams returns product-specific risk check parameters
    GetRiskCheckParams(ctx context.Context, card *storage.Card) *riskAPI.ValidatePartnerTxnRequest
    
    // GetDefaultTransactionLimits returns product-specific default transaction limits
    GetDefaultTransactionLimits() map[string]int64
    
    // GetCardIssuanceParams returns product-specific card issuance parameters
    GetCardIssuanceParams(ctx context.Context, card *storage.Card, baseRequest *euronetAdapterAPI.CardIssuanceRequest) *euronetAdapterAPI.CardIssuanceRequest
    
    // GetCardLimits returns product-specific card count limits
    GetCardLimits() *CardLimits
    
    // GetProductConfig returns the product configuration
    GetProductConfig() *constant.ProductConfig
}

// CardLimits defines card count limits for a product
type CardLimits struct {
    MaxActiveCards int
    MaxTotalCards  int
}

// RiskCheckParams holds risk check parameters
type RiskCheckParams struct {
    TransactionDomain string
    TransactionType   string
    ProductType       string
}
```

### Step 2: Create Debit Card Strategy

**File**: `digicard-core/logic/card/create/strategy/debit_strategy.go`
```go
package strategy

import (
    "context"
    
    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/enquiry"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
    riskAPI "gitlab.myteksi.net/dakota/client-sdk/risk-service"
    euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
    accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// DebitCardStrategy implements ProductStrategy for debit cards
type DebitCardStrategy struct {
    AccountServiceClient accountAPIV2.AccountService
    ProductConfig        *constant.ProductConfig
}

// NewDebitCardStrategy creates a new debit card strategy
func NewDebitCardStrategy(accountService accountAPIV2.AccountService) *DebitCardStrategy {
    return &DebitCardStrategy{
        AccountServiceClient: accountService,
        ProductConfig:        constant.GetProductConfig(constant.MYDebitCard),
    }
}

// ValidateAccount performs standard CASA account validation for debit cards
func (s *DebitCardStrategy) ValidateAccount(ctx context.Context, accountID string) error {
    return enquiry.CheckCasaAccount(ctx, accountID, s.AccountServiceClient)
}

// GetRiskCheckParams returns debit card specific risk check parameters
func (s *DebitCardStrategy) GetRiskCheckParams(ctx context.Context, card *storage.Card) *riskAPI.ValidatePartnerTxnRequest {
    return &riskAPI.ValidatePartnerTxnRequest{
        CustomerID:        card.UserID,
        AccountID:         card.AccountID,
        CardID:            card.CardID,
        TransactionDomain: s.ProductConfig.CardIssuanceConfig.TransactionDomain,
        TransactionType:   s.ProductConfig.CardIssuanceConfig.TransactionType,
        ProductType:       "DEBIT_CARD",
    }
}

// GetDefaultTransactionLimits returns default limits for debit cards
func (s *DebitCardStrategy) GetDefaultTransactionLimits() map[string]int64 {
    limitNames := s.ProductConfig.TxnLimitNames
    return map[string]int64{
        limitNames.Contactless: 25000,  // 250 MYR
        limitNames.Online:      50000,  // 500 MYR
        limitNames.PinAndPay:   25000,  // 250 MYR
        limitNames.ATM:         150000, // 1500 MYR
    }
}

// GetCardIssuanceParams returns debit card specific issuance parameters
func (s *DebitCardStrategy) GetCardIssuanceParams(ctx context.Context, card *storage.Card, baseRequest *euronetAdapterAPI.CardIssuanceRequest) *euronetAdapterAPI.CardIssuanceRequest {
    baseRequest.ProductVariant = string(constant.MYDebitCard)
    baseRequest.VendorIdentifier = s.ProductConfig.VendorIdentifier // "MDP"
    return baseRequest
}

// GetCardLimits returns debit card count limits
func (s *DebitCardStrategy) GetCardLimits() *CardLimits {
    return &CardLimits{
        MaxActiveCards: 2,
        MaxTotalCards:  99,
    }
}

// GetProductConfig returns the debit card product configuration
func (s *DebitCardStrategy) GetProductConfig() *constant.ProductConfig {
    return s.ProductConfig
}
```
