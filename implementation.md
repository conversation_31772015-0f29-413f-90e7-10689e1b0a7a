# Implementation Plan: Virtual Card Creation for CCC

## Prerequisites

### Environment Variables
```bash
# Customer Master Service
CUSTOMER_MASTER_BASE_URL=https://backend.dev.g-bank.app/customer-master
CUSTOMER_MASTER_SERVICE_NAME=customer-master

# EN Service Configuration
EN_SERVICE_TIMEOUT_MS=30000
EN_SERVICE_RETRY_ATTEMPTS=3
EN_SERVICE_RETRY_INTERVALS=5000,10000,20000

# Default Transaction Limits for CCC
CCC_DEFAULT_ONLINE_LIMIT=1000
CCC_DEFAULT_CONTACTLESS_LIMIT=500
CCC_DEFAULT_ATM_LIMIT=300
CCC_DEFAULT_PINPAY_LIMIT=500
```

### Configuration Updates
Add to `digicard-core/config_files/service-conf.json`:
```json
{
  "cccCardCreationConfig": {
    "defaultLimits": {
      "online": 100000,
      "contactless": 50000,
      "atm": 30000,
      "pinpay": 50000
    },
    "retryPolicy": {
      "maxAttempts": 3,
      "intervalInSeconds": [5, 10, 20]
    },
    "timeoutInSeconds": 30
  }
}
```

## Step-by-Step Implementation

### Step 1: Add Product Variant Constants

**File**: `common/constant/product_variant.go`
```go
package constant

// Product variant constants for different card types
const (
    // ProductVariantRegular for standard debit cards
    ProductVariantRegular = "CASA_DEBIT_CARD"

    // ProductVariantCCC for Credit Card Customer virtual cards
    ProductVariantCCC = "CCC_VIRTUAL_CARD"

    // ProductVariantCCCPhysical for Credit Card Customer physical cards (future)
    ProductVariantCCCPhysical = "CCC_PHYSICAL_CARD"
)

// Note: CCC cards use existing CardStatus constants:
// - CardStatusProcessing for card creation in progress
// - CardStatusActive for successfully created cards
// - CardStatusFailed for failed card creation
```

**No Database Migration Required**: The existing `card` table already has:
- `product_variant` column for distinguishing CCC cards
- `account_id` column for storing RLOC account ID
- `reference_id` column for idempotency tracking
- `status` column for CCC-specific statuses
- `post_script` JSON column for retry metadata

### Step 2: Update API Definition

**File**: `digicard-core/api/digicard_core.proto`

Add new message types:
```protobuf
message CreateVirtualCardCCCRequest {
    string preferredName = 1 [(gxs.api.validate) = "string,max=32,required"];
    bool enableOnlineTransactions = 2;
    string idempotencyKey = 3 [(gxs.api.validate) = "string,max=36,required"];
}

message CreateVirtualCardCCCResponse {
    CardDetail card = 1;
    string status = 2;
    string statusReason = 3;
    string statusReasonDescription = 4;
    string rlocAccountId = 5;
}

message GetCardCreationStatusRequest {
    string idempotencyKey = 1 [(gxs.api.validate) = "string,max=36,required"];
}

message GetCardCreationStatusResponse {
    string status = 1;
    string statusReason = 2;
    int32 attemptCount = 3;
    string lastAttemptAt = 4;
}
```

Add new RPC methods:
```protobuf
rpc CreateVirtualCardCCC(CreateVirtualCardCCCRequest) returns (CreateVirtualCardCCCResponse) {
    option (google.api.http) = {
        post: "/v1/cards/virtual/ccc",
        body: "*",
    };
    option(gxs.api.auth) = {
        client_identities: [
            "servicename.SentryT6"
        ]
    };
}

rpc GetCardCreationStatus(GetCardCreationStatusRequest) returns (GetCardCreationStatusResponse) {
    option (google.api.http) = {
        get: "/v1/cards/creation-status/{idempotencyKey}"
    };
    option(gxs.api.auth) = {
        client_identities: [
            "servicename.SentryT6"
        ]
    };
}
```

### Step 3: Update Card Storage Helper

**File**: `digicard-core/logic/card/create/ccc_helper.go`
```go
package createcard

import (
    "context"
    "encoding/json"
    "time"

    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
    "gitlab.myteksi.net/dakota/servus/v2/data"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

// CCCRetryMetadata stores retry information in the post_script JSON field
type CCCRetryMetadata struct {
    AttemptCount    int       `json:"attemptCount"`
    LastAttemptAt   time.Time `json:"lastAttemptAt"`
    ErrorDetails    string    `json:"errorDetails,omitempty"`
    RlocAccountID   string    `json:"rlocAccountId"`
}

// FindCCCCardByIdempotencyKey finds a CCC card by reference_id (idempotency key)
func FindCCCCardByIdempotencyKey(ctx context.Context, idempotencyKey string) (*storage.Card, error) {
    query := []data.Condition{
        data.EqualTo("reference_id", idempotencyKey),
        data.EqualTo("product_variant", constant.ProductVariantCCC),
    }

    card, err := storage.CardDao.Find(ctx, query...)
    if err != nil {
        return nil, err
    }
    return card, nil
}

// UpdateCCCCardStatus updates the status and retry metadata for a CCC card
func UpdateCCCCardStatus(ctx context.Context, card *storage.Card, status string, retryMeta *CCCRetryMetadata) error {
    updatedCard := *card
    updatedCard.Status = status
    updatedCard.UpdatedAt = time.Now()

    if retryMeta != nil {
        metaJSON, err := json.Marshal(retryMeta)
        if err != nil {
            slog.FromContext(ctx).Error("ccc_helper", "Failed to marshal retry metadata", slog.Error(err))
            return err
        }
        updatedCard.PostScript = metaJSON
    }

    return storage.CardDao.UpdateEntity(ctx, card, &updatedCard)
}

// GetCCCRetryMetadata extracts retry metadata from the post_script field
func GetCCCRetryMetadata(card *storage.Card) (*CCCRetryMetadata, error) {
    if len(card.PostScript) == 0 {
        return &CCCRetryMetadata{}, nil
    }

    var meta CCCRetryMetadata
    if err := json.Unmarshal(card.PostScript, &meta); err != nil {
        return nil, err
    }

    return &meta, nil
}

// IsCCCCard checks if a card is a CCC card based on product variant
func IsCCCCard(card *storage.Card) bool {
    return card.ProductVariant == constant.ProductVariantCCC
}
```

### Step 4: Create RLOC Client

**File**: `digicard-core/logic/card/create/rloc_client.go`
```go
package createcard

import (
    "context"
    "encoding/json"
    "fmt"

    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
    customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

// RlocAccount represents RLOC account information
type RlocAccount struct {
    AccountID       string `json:"accountId"`
    CreditLimit     int64  `json:"creditLimit"`
    AvailableBalance int64 `json:"availableBalance"`
    Status          string `json:"status"`
}

// RlocClient defines the interface for RLOC operations
type RlocClient interface {
    GetRlocAccount(ctx context.Context, customerID string) (*RlocAccount, error)
}

// RlocClientImpl implements RlocClient
type RlocClientImpl struct {
    CustomerMasterClient customerMasterDBMYAPI.CustomerMaster `inject:"client.customerMasterDBMY"`
}

// NewRlocClient creates a new RLOC client
func NewRlocClient() RlocClient {
    return &RlocClientImpl{}
}

// GetRlocAccount fetches RLOC account information for a customer
func (r *RlocClientImpl) GetRlocAccount(ctx context.Context, customerID string) (*RlocAccount, error) {
    slog.FromContext(ctx).Info("rloc_client", "fetching RLOC account for customer", slog.CustomTag("customerID", customerID))
    
    // Get customer information from customer-master
    getCustomerReq := &customerMasterDBMYAPI.GetCustomerRequest{
        ID: customerID,
        Target: &customerMasterDBMYAPI.TargetGroup{
            ServiceID: constant.ServiceID,
        },
    }
    
    customerObj, err := r.CustomerMasterClient.GetCustomer(ctx, getCustomerReq)
    if err != nil {
        slog.FromContext(ctx).Error("rloc_client", "failed to get customer from customer-master", slog.Error(err))
        return nil, api.DefaultInternalServerError
    }
    
    customerData := &customerMasterDBMYAPI.Customer{}
    if err = json.Unmarshal(customerObj.Customer.Data, customerData); err != nil {
        slog.FromContext(ctx).Error("rloc_client", "failed to unmarshal customer data", slog.Error(err))
        return nil, api.DefaultInternalServerError
    }
    
    // Extract RLOC account information
    // This is a simplified implementation - actual logic would depend on customer-master schema
    rlocAccount := &RlocAccount{
        AccountID:       extractRlocAccountID(customerData),
        CreditLimit:     extractCreditLimit(customerData),
        AvailableBalance: extractAvailableBalance(customerData),
        Status:          extractAccountStatus(customerData),
    }
    
    if rlocAccount.AccountID == "" {
        return nil, fmt.Errorf("no RLOC account found for customer %s", customerID)
    }
    
    slog.FromContext(ctx).Info("rloc_client", "successfully fetched RLOC account", 
        slog.CustomTag("accountID", rlocAccount.AccountID))
    
    return rlocAccount, nil
}

// Helper functions to extract RLOC information from customer data
func extractRlocAccountID(customer *customerMasterDBMYAPI.Customer) string {
    // Implementation depends on customer-master schema
    // This is a placeholder
    return "RLOC_" + customer.GetId()
}

func extractCreditLimit(customer *customerMasterDBMYAPI.Customer) int64 {
    // Implementation depends on customer-master schema
    return 500000 // Default 5000 MYR in cents
}

func extractAvailableBalance(customer *customerMasterDBMYAPI.Customer) int64 {
    // Implementation depends on customer-master schema
    return 500000 // Default available balance
}

func extractAccountStatus(customer *customerMasterDBMYAPI.Customer) string {
    // Implementation depends on customer-master schema
    return "ACTIVE"
}
```

### Step 5: Create CCC Workflow

**File**: `digicard-core/logic/card/create/ccc_workflow.go`
```go
package createcard

import (
    "context"
    "errors"
    "fmt"
    "time"

    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
    commonCtx "gitlab.myteksi.net/dakota/common/context"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
    "gitlab.myteksi.net/dakota/workflowengine"
)

// CCCExecutionData extends ExecutionData for CCC-specific workflow
type CCCExecutionData struct {
    *ExecutionData
    RlocAccount     *RlocAccount `json:"rlocAccount"`
    PreferredName   string       `json:"preferredName"`
    EnableOnline    bool         `json:"enableOnline"`
    CreationStatus  *storage.CardCreationStatus `json:"creationStatus"`
}

// ExecuteCreateVirtualCardCCCWorkflow handles CCC virtual card creation
func (w *WorkflowImpl) ExecuteCreateVirtualCardCCCWorkflow(ctx context.Context, req *api.CreateVirtualCardCCCRequest) (*api.CreateVirtualCardCCCResponse, error) {
    userID := commonCtx.GetUserID(ctx)

    // Check for existing card with same idempotency key
    existingCard, err := FindCCCCardByIdempotencyKey(ctx, req.IdempotencyKey)
    if err == nil {
        // Card already exists, handle based on current status
        return w.handleExistingCCCCard(ctx, existingCard)
    }
    if err != data.ErrNoData {
        // Unexpected error
        return nil, api.DefaultInternalServerError
    }

    // Fetch RLOC account information
    rlocAccount, err := w.RlocClient.GetRlocAccount(ctx, userID)
    if err != nil {
        w.updateCreationStatus(ctx, creationStatus, "FAILED", fmt.Sprintf("RLOC fetch failed: %v", err))
        return &api.CreateVirtualCardCCCResponse{
            Status:       "FAILED",
            StatusReason: "RLOC_ACCOUNT_NOT_FOUND",
        }, nil
    }

    // Validate card limits
    if err := w.validateCCCCardLimits(ctx, userID); err != nil {
        w.updateCreationStatus(ctx, creationStatus, "FAILED", err.Error())
        return &api.CreateVirtualCardCCCResponse{
            Status:       "FAILED",
            StatusReason: "CARD_LIMIT_EXCEEDED",
        }, nil
    }

    // Create initial card record with CCC product variant
    initialCard := &storage.Card{
        UserID:         userID,
        CardID:         uuid.NewString(),
        AccountID:      rlocAccount.AccountID,
        ProductVariant: constant.ProductVariantCCC,
        DisplayName:    req.PreferredName,
        Status:         string(constant.CardStatusProcessing),
        ReferenceID:    req.IdempotencyKey,
        CreatedAt:      time.Now(),
        UpdatedAt:      time.Now(),
    }

    // Save initial card record
    if err := storage.CardDao.Create(ctx, initialCard); err != nil {
        return &api.CreateVirtualCardCCCResponse{
            Status:       "FAILED",
            StatusReason: "CARD_CREATION_FAILED",
        }, nil
    }

    // Start workflow execution
    return w.executeCCCCardCreation(ctx, initialCard, rlocAccount, req)
}

func (w *WorkflowImpl) executeCCCCardCreation(ctx context.Context, card *storage.Card, rlocAccount *RlocAccount, req *api.CreateVirtualCardCCCRequest) (*api.CreateVirtualCardCCCResponse, error) {
    // Execute card creation with retry logic
    for attempt := 1; attempt <= 3; attempt++ {
        retryMeta := &CCCRetryMetadata{
            AttemptCount:  attempt,
            LastAttemptAt: time.Now(),
            RlocAccountID: rlocAccount.AccountID,
        }

        // Update card status to show retry attempt (using existing status constants)
        status := string(constant.CardStatusProcessing)
        UpdateCCCCardStatus(ctx, card, status, retryMeta)

        result, err := w.attemptCCCCardCreation(ctx, card, rlocAccount, req)
        if err == nil {
            // Success - update card to ACTIVE status
            UpdateCCCCardStatus(ctx, card, string(constant.CardStatusActive), retryMeta)
            return result, nil
        }

        if !w.isRetryableError(err) {
            // Non-retryable error
            retryMeta.ErrorDetails = err.Error()
            UpdateCCCCardStatus(ctx, card, string(constant.CardStatusFailed), retryMeta)
            return &api.CreateVirtualCardCCCResponse{
                Status:       "FAILED",
                StatusReason: "CARD_CREATION_FAILED",
            }, nil
        }

        // Retryable error - wait before next attempt
        if attempt < 3 {
            waitTime := time.Duration(attempt*5) * time.Second
            slog.FromContext(ctx).Info("ccc_workflow", fmt.Sprintf("Retrying card creation in %v", waitTime))
            time.Sleep(waitTime)
        }
    }

    // All retries exhausted
    retryMeta := &CCCRetryMetadata{
        AttemptCount:  3,
        LastAttemptAt: time.Now(),
        ErrorDetails:  "Maximum retry attempts exceeded",
        RlocAccountID: rlocAccount.AccountID,
    }
    UpdateCCCCardStatus(ctx, card, string(constant.CardStatusFailed), retryMeta)
    return &api.CreateVirtualCardCCCResponse{
        Status:       "PROCESSING", // Keep as processing for background retry
        StatusReason: "RETRY_IN_PROGRESS",
    }, nil
}

func (w *WorkflowImpl) attemptCardCreation(ctx context.Context, data *CCCExecutionData) (*api.CreateVirtualCardCCCResponse, error) {
    // Create card via EN service
    cardReq := &api.CreateCardRequest{
        AccountID:      data.RlocAccount.AccountID,
        DisplayName:    data.PreferredName,
        Type:           string(constant.VirtualCard),
        IdempotencyKey: fmt.Sprintf("%s_%d", data.IdempotencyKey, data.CreationStatus.AttemptCount),
        CnpEnabled:     data.EnableOnline,
    }

    cardResp, err := w.CreateCardWorkflow.ExecuteCreateCardWorkflow(ctx, cardReq)
    if err != nil {
        return nil, err
    }

    if cardResp.Status != constant.Success {
        return nil, fmt.Errorf("card creation failed: %s", cardResp.StatusReason)
    }

    // Set default transaction limits
    if err := w.setDefaultCCCLimits(ctx, cardResp.Card.CardID, data.EnableOnline); err != nil {
        slog.FromContext(ctx).Warn("ccc_workflow", "Failed to set default limits", slog.Error(err))
        // Don't fail the entire operation for limit setting failure
    }

    // Send success notification
    w.sendCCCSuccessNotification(ctx, data, cardResp.Card)

    return &api.CreateVirtualCardCCCResponse{
        Card:          cardResp.Card,
        Status:        "SUCCESS",
        StatusReason:  "CARD_CREATED_SUCCESSFULLY",
        RlocAccountId: data.RlocAccount.AccountID,
    }, nil
}

func (w *WorkflowImpl) validateCCCCardLimits(ctx context.Context, userID string) error {
    cards, err := logic.FetchAllCards(ctx, userID)
    if err != nil {
        return err
    }

    activeCount := 0
    for _, card := range cards {
        if card.Status == string(constant.CardStatusActive) || card.Status == string(constant.CardStatusProcessing) {
            activeCount++
        }
    }

    if activeCount >= 2 { // CCC limit
        return errors.New("maximum active cards limit reached for CCC customer")
    }

    return nil
}

func (w *WorkflowImpl) isRetryableError(err error) bool {
    // Define retryable error patterns
    retryablePatterns := []string{
        "timeout",
        "connection refused",
        "service unavailable",
        "internal server error",
    }

    errStr := err.Error()
    for _, pattern := range retryablePatterns {
        if contains(errStr, pattern) {
            return true
        }
    }
    return false
}

func (w *WorkflowImpl) updateCreationStatus(ctx context.Context, status *storage.CardCreationStatus, newStatus, reason string) {
    status.Status = newStatus
    if reason != "" {
        errorDetails := map[string]string{"reason": reason}
        status.ErrorDetails, _ = json.Marshal(errorDetails)
    }
    storage.CardCreationStatusDao.Update(ctx, status)
}

func (w *WorkflowImpl) handleExistingCCCCard(ctx context.Context, card *storage.Card) (*api.CreateVirtualCardCCCResponse, error) {
    switch card.Status {
    case string(constant.CardStatusActive):
        // Return existing successful card
        return &api.CreateVirtualCardCCCResponse{
            Card:          convertToAPICardDetail(card),
            Status:        "SUCCESS",
            StatusReason:  "CARD_ALREADY_EXISTS",
            RlocAccountId: card.AccountID,
        }, nil
    case string(constant.CardStatusProcessing):
        return &api.CreateVirtualCardCCCResponse{
            Status:       "PROCESSING",
            StatusReason: "CARD_CREATION_IN_PROGRESS",
        }, nil
    case string(constant.CardStatusFailed):
        return &api.CreateVirtualCardCCCResponse{
            Status:       "FAILED",
            StatusReason: "PREVIOUS_ATTEMPT_FAILED",
        }, nil
    default:
        return &api.CreateVirtualCardCCCResponse{
            Status:       "PROCESSING",
            StatusReason: "REQUEST_BEING_PROCESSED",
        }, nil
    }
}

func convertToAPICardDetail(card *storage.Card) *api.CardDetail {
    return &api.CardDetail{
        CardID:         card.CardID,
        TailCardNumber: card.TailCardNumber,
        HeadCardNumber: card.HeadCardNumber,
        DisplayName:    card.DisplayName,
        Status:         card.Status,
        AccountID:      card.AccountID,
        VendorCardID:   card.VendorCardID,
        OrderStatus:    card.OrderStatus,
        CardType:       "VIRTUAL",
    }
}

func contains(s, substr string) bool {
    return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) &&
        (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
         (len(s) > len(substr) && s[1:len(substr)+1] == substr))))
}
```

### Step 6: Create Limit Setter

**File**: `digicard-core/logic/card/create/limit_setter.go`
```go
package createcard

import (
    "context"
    "fmt"

    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

// LimitSetter handles setting default transaction limits for CCC cards
type LimitSetter interface {
    SetDefaultCCCLimits(ctx context.Context, cardID string, enableOnline bool) error
}

// LimitSetterImpl implements LimitSetter
type LimitSetterImpl struct {
    TransactionLimitClient transactionLimitAPI.TransactionLimit `inject:"client.transactionLimit"`
}

// NewLimitSetter creates a new limit setter
func NewLimitSetter() LimitSetter {
    return &LimitSetterImpl{}
}

// SetDefaultCCCLimits sets default transaction limits for CCC virtual cards
func (l *LimitSetterImpl) SetDefaultCCCLimits(ctx context.Context, cardID string, enableOnline bool) error {
    slog.FromContext(ctx).Info("limit_setter", "Setting default CCC limits", slog.CustomTag("cardID", cardID))

    // Default limits for CCC customers (in cents)
    limits := map[string]int64{
        constant.TxnLimitContactless:   50000,  // 500 MYR
        constant.TxnLimitPinAndPay:     50000,  // 500 MYR
        constant.TxnLimitAtmWithdrawal: 30000,  // 300 MYR
    }

    // Set online limit based on user preference
    if enableOnline {
        limits[constant.TxnLimitOnline] = 100000 // 1000 MYR
    } else {
        limits[constant.TxnLimitOnline] = 0 // Disabled
    }

    // Set each limit
    for limitName, amount := range limits {
        if err := l.setTransactionLimit(ctx, cardID, limitName, amount); err != nil {
            slog.FromContext(ctx).Error("limit_setter", "Failed to set limit",
                slog.CustomTag("limitName", limitName), slog.Error(err))
            return err
        }
    }

    slog.FromContext(ctx).Info("limit_setter", "Successfully set all default CCC limits")
    return nil
}

func (l *LimitSetterImpl) setTransactionLimit(ctx context.Context, cardID, limitName string, amount int64) error {
    req := &transactionLimitAPI.SetTransactionLimitRequest{
        LimitName: limitName,
        Amount:    amount,
        Currency:  "MYR",
        Conditions: []transactionLimitAPI.Condition{
            {
                Key:   constant.TxnLimitConditionKey,
                Value: cardID,
            },
        },
    }

    _, err := l.TransactionLimitClient.SetTransactionLimit(ctx, req)
    if err != nil {
        return fmt.Errorf("failed to set %s limit: %w", limitName, err)
    }

    return nil
}
```

### Step 7: Create Handler

**File**: `digicard-core/handlers/digicardcore_create_virtual_card_ccc.handler.go`
```go
package handlers

import (
    "context"

    "gitlab.com/gx-regional/dbmy/digicard/common/recorder"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/common"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/create"
    commonCtx "gitlab.myteksi.net/dakota/common/context"
    "gitlab.myteksi.net/dakota/servus/v2"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
    createVirtualCardCCCLogTag = "create_virtual_card_ccc_handler"
)

// CreateVirtualCardCCC creates a virtual card for CCC customers
func (d *DigicardCoreService) CreateVirtualCardCCC(ctx context.Context, req *api.CreateVirtualCardCCCRequest) (*api.CreateVirtualCardCCCResponse, error) {
    recorder.RecordApiFuncName(ctx, common.CreateVirtualCardCCC)

    // Validate user ID
    if commonCtx.GetUserID(ctx) == "" {
        errorCode := api.Forbidden
        recorder.RecordResponseCode(ctx, string(errorCode))
        return nil, servus.ServiceError{
            Code:     string(errorCode),
            Message:  "'X-Grab-Id-Userid' is missing.",
            HTTPCode: errorCode.HTTPStatusCode(),
        }
    }

    // Validate request parameters
    if errs := validateCreateVirtualCardCCCRequest(req); len(errs) != 0 {
        errorCode := api.BadRequest
        recorder.RecordResponseCode(ctx, string(errorCode))
        return nil, servus.ServiceError{
            Code:     string(errorCode),
            Message:  "request has invalid parameter(s) or header(s).",
            HTTPCode: errorCode.HTTPStatusCode(),
            Errors:   errs,
        }
    }

    // Check whitelist
    if !d.WhitelistClient.IsUserWhitelisted(ctx, commonCtx.GetUserID(ctx)) {
        errorCode := api.Forbidden
        recorder.RecordResponseCode(ctx, string(errorCode))
        return nil, servus.ServiceError{
            Code:     string(errorCode),
            Message:  "user is not whitelisted to create CCC card",
            HTTPCode: errorCode.HTTPStatusCode(),
        }
    }

    // Execute CCC card creation workflow
    resp, err := d.CCCCardCreationWorkflow.ExecuteCreateVirtualCardCCCWorkflow(ctx, req)
    if err != nil {
        slog.FromContext(ctx).Error(createVirtualCardCCCLogTag, "CCC card creation failed", slog.Error(err))
        recorder.RecordResponseCode(ctx, err.Error())
        return nil, err
    }

    recorder.RecordResponseCode(ctx, resp.StatusReason)
    return resp, nil
}

// GetCardCreationStatus returns the status of a card creation request
func (d *DigicardCoreService) GetCardCreationStatus(ctx context.Context, req *api.GetCardCreationStatusRequest) (*api.GetCardCreationStatusResponse, error) {
    card, err := createcard.FindCCCCardByIdempotencyKey(ctx, req.IdempotencyKey)
    if err != nil {
        return nil, err
    }

    retryMeta, err := createcard.GetCCCRetryMetadata(card)
    if err != nil {
        return nil, err
    }

    return &api.GetCardCreationStatusResponse{
        Status:        card.Status,
        StatusReason:  retryMeta.ErrorDetails,
        AttemptCount:  int32(retryMeta.AttemptCount),
        LastAttemptAt: retryMeta.LastAttemptAt.Format("2006-01-02T15:04:05Z"),
    }, nil
}

func validateCreateVirtualCardCCCRequest(req *api.CreateVirtualCardCCCRequest) []servus.ErrorDetail {
    var errs []servus.ErrorDetail

    emptyFieldValidator(req.IdempotencyKey, "idempotencyKey", &errs)
    emptyFieldValidator(req.PreferredName, "preferredName", &errs)

    if req.PreferredName != "" && !isValidDisplayName(req.PreferredName) {
        errs = append(errs, servus.ErrorDetail{
            ErrorCode: string(api.FieldInvalid),
            Message:   "invalid preferred name",
            Path:      "preferredName",
        })
    }

    return errs
}
```

## Testing

### Unit Tests
1. **RLOC Client Tests**: Mock customer-master responses
2. **CCC Workflow Tests**: Test retry logic and error handling
3. **Limit Setter Tests**: Verify default limits are set correctly
4. **Handler Tests**: Validate request/response handling

### Integration Tests
1. **End-to-End Flow**: Complete CCC card creation process
2. **Retry Scenarios**: Test timeout and retry mechanisms
3. **Error Handling**: Test various failure scenarios
4. **Idempotency**: Verify duplicate request handling

### Test Commands
```bash
# Run unit tests
go test ./digicard-core/logic/card/create/... -v

# Run integration tests
go test ./digicard-core/test/api/... -v -tags=integration

# Run specific CCC tests
go test ./digicard-core/handlers/... -run TestCreateVirtualCardCCC -v
```
```
