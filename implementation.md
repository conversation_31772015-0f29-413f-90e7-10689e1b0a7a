# Implementation Plan: Virtual Card Creation for CCC

## Prerequisites

### Environment Variables
```bash
# Customer Master Service
CUSTOMER_MASTER_BASE_URL=https://backend.dev.g-bank.app/customer-master
CUSTOMER_MASTER_SERVICE_NAME=customer-master

# EN Service Configuration
EN_SERVICE_TIMEOUT_MS=30000
EN_SERVICE_RETRY_ATTEMPTS=3
EN_SERVICE_RETRY_INTERVALS=5000,10000,20000

# Default Transaction Limits for CCC
CCC_DEFAULT_ONLINE_LIMIT=1000
CCC_DEFAULT_CONTACTLESS_LIMIT=500
CCC_DEFAULT_ATM_LIMIT=300
CCC_DEFAULT_PINPAY_LIMIT=500
```

### Configuration Updates
Add to `digicard-core/config_files/service-conf.json`:
```json
{
  "cccCardCreationConfig": {
    "defaultLimits": {
      "online": 100000,
      "contactless": 50000,
      "atm": 30000,
      "pinpay": 50000
    },
    "retryPolicy": {
      "maxAttempts": 3,
      "intervalInSeconds": [5, 10, 20]
    },
    "timeoutInSeconds": 30
  }
}
```

## Step-by-Step Implementation

### Step 1: Database Migration

**File**: `digicard-core/db/mysql/deploy/0002-ccc-card-creation.sql`
```sql
-- Add new columns to existing cards table
ALTER TABLE `card` 
ADD COLUMN `customer_type` VARCHAR(10) DEFAULT 'REGULAR' COMMENT 'Customer type: REGULAR, CCC',
ADD COLUMN `rloc_account_id` VARCHAR(36) DEFAULT NULL COMMENT 'Associated RLOC account ID',
ADD COLUMN `card_creation_attempt_count` INT DEFAULT 0 COMMENT 'Number of creation attempts';

-- Create card creation status tracking table
CREATE TABLE `card_creation_status` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `idempotency_key` VARCHAR(36) NOT NULL COMMENT 'Unique request identifier',
    `user_id` VARCHAR(36) NOT NULL COMMENT 'Customer ID',
    `status` VARCHAR(20) NOT NULL COMMENT 'PENDING, PROCESSING, SUCCESS, FAILED',
    `attempt_count` INT DEFAULT 0 COMMENT 'Number of attempts made',
    `last_attempt_at` DATETIME NULL COMMENT 'Last attempt timestamp',
    `error_details` JSON NULL COMMENT 'Error information for failed attempts',
    `rloc_account_id` VARCHAR(36) NULL COMMENT 'Associated RLOC account',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_idempotency_key` (`idempotency_key`),
    INDEX `idx_user_id_status` (`user_id`, `status`),
    INDEX `idx_status_last_attempt` (`status`, `last_attempt_at`)
) DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- Add indexes for performance
CREATE INDEX `idx_customer_type` ON `card` (`customer_type`);
CREATE INDEX `idx_rloc_account_id` ON `card` (`rloc_account_id`);
```

### Step 2: Update API Definition

**File**: `digicard-core/api/digicard_core.proto`

Add new message types:
```protobuf
message CreateVirtualCardCCCRequest {
    string preferredName = 1 [(gxs.api.validate) = "string,max=32,required"];
    bool enableOnlineTransactions = 2;
    string idempotencyKey = 3 [(gxs.api.validate) = "string,max=36,required"];
}

message CreateVirtualCardCCCResponse {
    CardDetail card = 1;
    string status = 2;
    string statusReason = 3;
    string statusReasonDescription = 4;
    string rlocAccountId = 5;
}

message GetCardCreationStatusRequest {
    string idempotencyKey = 1 [(gxs.api.validate) = "string,max=36,required"];
}

message GetCardCreationStatusResponse {
    string status = 1;
    string statusReason = 2;
    int32 attemptCount = 3;
    string lastAttemptAt = 4;
}
```

Add new RPC methods:
```protobuf
rpc CreateVirtualCardCCC(CreateVirtualCardCCCRequest) returns (CreateVirtualCardCCCResponse) {
    option (google.api.http) = {
        post: "/v1/cards/virtual/ccc",
        body: "*",
    };
    option(gxs.api.auth) = {
        client_identities: [
            "servicename.SentryT6"
        ]
    };
}

rpc GetCardCreationStatus(GetCardCreationStatusRequest) returns (GetCardCreationStatusResponse) {
    option (google.api.http) = {
        get: "/v1/cards/creation-status/{idempotencyKey}"
    };
    option(gxs.api.auth) = {
        client_identities: [
            "servicename.SentryT6"
        ]
    };
}
```

### Step 3: Create Storage Layer

**File**: `digicard-core/storage/card_creation_status.go`
```go
package storage

import (
    "encoding/json"
    "time"
)

// CardCreationStatus represents the card creation status tracking
type CardCreationStatus struct {
    ID              uint64          `sql-col:"id" sql-key:"id" sql-insert:"false"`
    IdempotencyKey  string          `sql-col:"idempotency_key"`
    UserID          string          `sql-col:"user_id"`
    Status          string          `sql-col:"status"`
    AttemptCount    int             `sql-col:"attempt_count"`
    LastAttemptAt   *time.Time      `sql-col:"last_attempt_at"`
    ErrorDetails    json.RawMessage `sql-col:"error_details" sql-where:"false" data-type:"json"`
    RlocAccountID   *string         `sql-col:"rloc_account_id"`
    CreatedAt       time.Time       `sql-col:"created_at"`
    UpdatedAt       time.Time       `sql-col:"updated_at"`
}

// ICardCreationStatusDAO defines the interface for card creation status operations
type ICardCreationStatusDAO interface {
    Create(ctx context.Context, status *CardCreationStatus) error
    Update(ctx context.Context, status *CardCreationStatus) error
    FindByIdempotencyKey(ctx context.Context, key string) (*CardCreationStatus, error)
    FindByUserIDAndStatus(ctx context.Context, userID, status string) ([]*CardCreationStatus, error)
}

//go:generate mockery --name=ICardCreationStatusDAO --case=underscore

// CardCreationStatusDAO is the implementation of ICardCreationStatusDAO
var CardCreationStatusDao ICardCreationStatusDAO
```

### Step 4: Create RLOC Client

**File**: `digicard-core/logic/card/create/rloc_client.go`
```go
package createcard

import (
    "context"
    "encoding/json"
    "fmt"

    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
    customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

// RlocAccount represents RLOC account information
type RlocAccount struct {
    AccountID       string `json:"accountId"`
    CreditLimit     int64  `json:"creditLimit"`
    AvailableBalance int64 `json:"availableBalance"`
    Status          string `json:"status"`
}

// RlocClient defines the interface for RLOC operations
type RlocClient interface {
    GetRlocAccount(ctx context.Context, customerID string) (*RlocAccount, error)
}

// RlocClientImpl implements RlocClient
type RlocClientImpl struct {
    CustomerMasterClient customerMasterDBMYAPI.CustomerMaster `inject:"client.customerMasterDBMY"`
}

// NewRlocClient creates a new RLOC client
func NewRlocClient() RlocClient {
    return &RlocClientImpl{}
}

// GetRlocAccount fetches RLOC account information for a customer
func (r *RlocClientImpl) GetRlocAccount(ctx context.Context, customerID string) (*RlocAccount, error) {
    slog.FromContext(ctx).Info("rloc_client", "fetching RLOC account for customer", slog.CustomTag("customerID", customerID))
    
    // Get customer information from customer-master
    getCustomerReq := &customerMasterDBMYAPI.GetCustomerRequest{
        ID: customerID,
        Target: &customerMasterDBMYAPI.TargetGroup{
            ServiceID: constant.ServiceID,
        },
    }
    
    customerObj, err := r.CustomerMasterClient.GetCustomer(ctx, getCustomerReq)
    if err != nil {
        slog.FromContext(ctx).Error("rloc_client", "failed to get customer from customer-master", slog.Error(err))
        return nil, api.DefaultInternalServerError
    }
    
    customerData := &customerMasterDBMYAPI.Customer{}
    if err = json.Unmarshal(customerObj.Customer.Data, customerData); err != nil {
        slog.FromContext(ctx).Error("rloc_client", "failed to unmarshal customer data", slog.Error(err))
        return nil, api.DefaultInternalServerError
    }
    
    // Extract RLOC account information
    // This is a simplified implementation - actual logic would depend on customer-master schema
    rlocAccount := &RlocAccount{
        AccountID:       extractRlocAccountID(customerData),
        CreditLimit:     extractCreditLimit(customerData),
        AvailableBalance: extractAvailableBalance(customerData),
        Status:          extractAccountStatus(customerData),
    }
    
    if rlocAccount.AccountID == "" {
        return nil, fmt.Errorf("no RLOC account found for customer %s", customerID)
    }
    
    slog.FromContext(ctx).Info("rloc_client", "successfully fetched RLOC account", 
        slog.CustomTag("accountID", rlocAccount.AccountID))
    
    return rlocAccount, nil
}

// Helper functions to extract RLOC information from customer data
func extractRlocAccountID(customer *customerMasterDBMYAPI.Customer) string {
    // Implementation depends on customer-master schema
    // This is a placeholder
    return "RLOC_" + customer.GetId()
}

func extractCreditLimit(customer *customerMasterDBMYAPI.Customer) int64 {
    // Implementation depends on customer-master schema
    return 500000 // Default 5000 MYR in cents
}

func extractAvailableBalance(customer *customerMasterDBMYAPI.Customer) int64 {
    // Implementation depends on customer-master schema
    return 500000 // Default available balance
}

func extractAccountStatus(customer *customerMasterDBMYAPI.Customer) string {
    // Implementation depends on customer-master schema
    return "ACTIVE"
}
```
