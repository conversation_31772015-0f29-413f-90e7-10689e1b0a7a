# Simplified Implementation Plan: CCC Virtual Card Creation

## Overview
Enhance the existing `POST /v1/card` endpoint to support CCC virtual card creation through minimal conditional logic based on the `product_variant` field, while maintaining full backward compatibility with the existing debit card flow.

## Implementation Strategy
1. **Reuse Existing API**: Use current `POST /v1/card` endpoint
2. **Product Variant Detection**: Leverage existing `GetProductConfig()` function  
3. **Minimal Conditional Logic**: Add simple if-else statements at key decision points
4. **Backward Compatibility**: Ensure existing debit card flow remains unchanged
5. **Use Existing Infrastructure**: Leverage existing database schema, constants, and services

## Key Principles
- **No New Tables**: Use existing `card` table structure
- **No New Constants**: Use existing `MYCreditCard` constant ("commercial_credit_card")
- **No New Statuses**: Use existing card statuses (PROCESSING, ACTIVE, FAILED)
- **No New Services**: Use existing account service data (no RLOC fetching)
- **No New Endpoints**: Enhance existing workflow only

## Implementation Points

### Point 1: Enhanced Workflow Initialization

**File**: `digicard-core/logic/card/create/create_card.go`
**Location**: Line 28 (existing TODO comment)

**Current Code**:
```go
data := &ExecutionData{
    State:          stInit,
    Request:        req,
    CustomerID:     userID,
    IdempotencyKey: req.IdempotencyKey,
    // TODO : GET Product variant from account service
}
```

**Enhanced Code**:
```go
// Get product variant from account service
accountReq := &accountAPIV2.GetAccountRequest{AccountID: req.AccountID}
accountResp, err := w.AccountServiceClient.GetAccountDetailsByAccountID(ctx, accountReq)
if err != nil {
    return nil, fmt.Errorf("failed to get account details: %w", err)
}

productVariant := constant.ProductVariant(accountResp.Account.ProductVariantID)
productConfig := constant.GetProductConfig(productVariant)

data := &ExecutionData{
    State:          stInit,
    Request:        req,
    CustomerID:     userID,
    IdempotencyKey: req.IdempotencyKey,
    ProductVariant: string(productVariant),        // Added: Store product variant
    ProductConfig:  productConfig,                 // Added: Store product config
}
```

### Point 2: Enhanced Account Verification

**File**: `digicard-core/logic/card/create/account_verify.go`
**Location**: Line 54 (existing TODO comment)

**Current Code**:
```go
// TODO: switch case for product variant
err = enquiry.CheckCasaAccount(ctx, accountID, c.AccountServiceClientV2)
if err != nil {
    return err
}
```

**Enhanced Code**:
```go
// Get product variant to determine account verification method
accountReq := &accountAPIV2.GetAccountRequest{AccountID: accountID}
accountResp, err := c.AccountServiceClientV2.GetAccountDetailsByAccountID(ctx, accountReq)
if err != nil {
    return fmt.Errorf("failed to get account details: %w", err)
}

productVariant := constant.ProductVariant(accountResp.Account.ProductVariantID)

// Product-specific account verification
if productVariant == constant.MYCreditCard {
    // For CCC: Verify account is active (account info already available)
    if accountResp.Account.Status != accountAPIV2.AccountStatus_ACTIVE {
        return fmt.Errorf("credit account is not active")
    }
} else {
    // For debit cards: Standard CASA account check
    err = enquiry.CheckCasaAccount(ctx, accountID, c.AccountServiceClientV2)
    if err != nil {
        return err
    }
}
```

### Point 3: Enhanced Risk Check Parameters

**File**: `digicard-core/logic/card/create/risk_check.go`
**Location**: Line 38 (convertCheckpointRequest function call)

**Current Code**:
```go
riskReq := convertCheckpointRequest(ctx, &nextCtx.Card, nextCtx.GetState())
```

**Enhanced Code**:
```go
// Use product variant for risk check parameters
productVariant := constant.ProductVariant(nextCtx.Card.ProductVariant)
riskReq := convertCheckpointRequestWithProduct(ctx, &nextCtx.Card, nextCtx.GetState(), productVariant)
```

**New Function**:
```go
func convertCheckpointRequestWithProduct(ctx context.Context, card *storage.Card, state we.State, productVariant constant.ProductVariant) *riskAPI.ValidatePartnerTxnRequest {
    productConfig := constant.GetProductConfig(productVariant)
    
    req := &riskAPI.ValidatePartnerTxnRequest{
        CustomerID: card.UserID,
        AccountID:  card.AccountID,
        CardID:     card.CardID,
    }
    
    // Set product-specific risk parameters from configuration
    if productConfig != nil {
        req.TransactionDomain = productConfig.CardIssuanceConfig.TransactionDomain
        req.TransactionType = productConfig.CardIssuanceConfig.TransactionType
        
        // Set product type based on variant
        if productVariant == constant.MYCreditCard {
            req.ProductType = "CREDIT_CARD"
        } else {
            req.ProductType = "DEBIT_CARD"
        }
    }
    
    return req
}
```

### Point 4: Enhanced Card Limits Validation

**File**: `digicard-core/logic/card/create/create_card.go`
**Location**: Line 46 (checkCardsLimit function)

**Current Code**:
```go
func executeVirtualCardCreation(ctx context.Context, data *ExecutionData, config config.CardIssuanceConfig) (*api.CardResponse, error) {
    if err := checkCardsLimit(ctx, data.CustomerID, config); err != nil {
        return nil, err
    }
    // ... rest of function
}
```

**Enhanced Code**:
```go
func executeVirtualCardCreation(ctx context.Context, data *ExecutionData, config config.CardIssuanceConfig) (*api.CardResponse, error) {
    // Use product-specific card limit checking
    if err := checkCardsLimitWithProduct(ctx, data.CustomerID, data.ProductVariant); err != nil {
        return nil, err
    }
    // ... rest of function
}

// Enhanced function with product variant support
func checkCardsLimitWithProduct(ctx context.Context, userID string, productVariant string) error {
    cards, err := logic.FetchAllCards(ctx, userID)
    if err != nil {
        return err
    }

    // Filter cards by product variant for accurate counting
    var productCards []*storage.Card
    for _, card := range cards {
        if card.ProductVariant == productVariant {
            productCards = append(productCards, card)
        }
    }
    
    activeCardsCount := 0
    for _, card := range productCards {
        if card.Status == string(constant.CardStatusProcessing) || card.Status == string(constant.CardStatusLocked) {
            return logic.ErrExceededMaxAllowedActiveCards
        }
        if card.Status == string(constant.CardStatusActive) {
            activeCardsCount++
        }
    }

    // Both CCC and debit cards have same limit (2 cards)
    if activeCardsCount >= 2 {
        return logic.ErrExceededMaxAllowedActiveCards
    }
    
    return nil
}
```
