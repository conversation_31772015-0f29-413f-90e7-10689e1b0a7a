# Technical Specification: Virtual Card Creation for CCC (Credit Card Customer)

## Objective

Implement virtual card creation functionality for Credit Card Customers (CCC) that integrates with customer-master to fetch RLOC (Revolving Line of Credit) account information and handles the complete card creation lifecycle including success, timeout, and error scenarios with appropriate retry mechanisms.

## Flow Analysis

### High-Level Flow
1. **User Initiation**: User provides preferred name and confirms intent to create virtual card
2. **Validation**: System validates user hasn't reached card limit and input parameters
3. **RLOC Integration**: Fetch customer RLOC account information from customer-master
4. **Card Creation Request**: Send cleaned-up card creation request to external service (EN)
5. **Response Handling**: Process EN response with timeout and retry logic
6. **Success Path**: Store card details, increment count, set limits, send notifications
7. **Failure Path**: Handle retryable/non-retryable errors with appropriate user feedback

### Detailed State Flow
```
[User Request] → [Validation] → [RLOC Fetch] → [EN Request] → [Response Processing]
                                                                      ↓
[Success] → [Store Card] → [Set Limits] → [Increment Count] → [Notify User]
     ↓
[Retryable Error] → [PROCESSING Status] → [Retry Logic] → [Final Resolution]
     ↓
[Non-Retryable Error] → [FAILED Status] → [Error Notification]
```

## Proposed Changes

### 1. New API Endpoint
- **Endpoint**: `POST /v1/cards/virtual/ccc`
- **Purpose**: Dedicated endpoint for CCC virtual card creation
- **Request Payload**:
  ```json
  {
    "preferredName": "string",
    "enableOnlineTransactions": "boolean",
    "idempotencyKey": "string"
  }
  ```
- **Response Payloads**:
  - **Success**: Card details with status "SUCCESS"
  - **Processing**: Status "PROCESSING" for retryable errors
  - **Failed**: Status "FAILED" for non-retryable errors

### 2. New Service Integrations

#### Customer-Master Integration
- **Endpoint**: `/v2/customers/{customerID}/rloc-accounts`
- **Purpose**: Fetch RLOC account information for CCC customers
- **Data Retrieved**: Account ID, credit limit, available balance, account status
- **Error Handling**: Fallback mechanisms for service unavailability

#### External Service (EN) Integration
- **Enhanced Request Contract**: Include RLOC account information
- **Timeout Configuration**: 30 seconds with exponential backoff
- **Retry Policy**: 3 attempts with 5s, 10s, 20s intervals
- **Response Handling**: Distinguish between retryable and non-retryable errors

### 3. Database Changes

#### Leverage Existing Card Table Structure
- Use existing `product_variant` field to distinguish CCC cards (e.g., "CCC_VIRTUAL_CARD")
- Store RLOC account ID in existing `account_id` field (already used for account association)
- Use existing `reference_id` field for idempotency tracking
- **Reuse existing `status` field** with standard card statuses: "PROCESSING", "ACTIVE", "FAILED"
- Store retry metadata in existing `post_script` JSON column

#### No New Tables or Statuses Required
The existing `card` table structure already supports all CCC requirements:
- Product differentiation via `product_variant` column
- Account association via `account_id` column
- Standard status tracking via existing `status` values (PROCESSING, ACTIVE, FAILED)
- Idempotency via `reference_id` column
- Retry metadata via `post_script` JSON column

## Affected Files & Modules

### Modified Files
- `digicard-core/api/digicard_core.proto` - Add new CCC card creation endpoint
- `digicard-core/handlers/digicardcore.routes.go` - Add new route
- `digicard-core/handlers/digicardcore.service.go` - Add dependencies
- `digicard-core/logic/card/create/create_card.go` - Enhance workflow for CCC
- `common/constant/product_variant.go` - Add CCC product variant constants

### New Files & Modules
- `digicard-core/handlers/digicardcore_create_virtual_card_ccc.handler.go`
- `digicard-core/logic/card/create/ccc_workflow.go`
- `digicard-core/logic/card/create/rloc_client.go`
- `digicard-core/logic/card/create/limit_setter.go`

## Error Handling Strategy

### Retry Mechanism (AC 3.2.1)
- **Retryable Errors**: Network timeouts, 5xx responses, temporary EN unavailability
- **Retry Policy**: Exponential backoff (5s, 10s, 20s)
- **Max Attempts**: 3 total attempts
- **Status**: Return "PROCESSING" to frontend during retries
- **User Blocking**: Prevent new creation attempts until current one resolves

### Non-Retryable Errors (AC 3.2.2)
- **Error Types**: Invalid customer data, account restrictions, permanent EN errors
- **Response**: Immediate "FAILED" status
- **Logging**: Detailed error categorization for monitoring
- **User Feedback**: Clear error messages for different failure scenarios

### Timeout Handling
- **EN Service Timeout**: 30 seconds per request
- **Total Workflow Timeout**: 5 minutes including retries
- **Background Processing**: Continue retries even if user session ends
- **Status Polling**: Allow frontend to check status via idempotency key
