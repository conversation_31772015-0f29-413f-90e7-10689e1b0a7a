# Technical Specification: Virtual Card Creation for CCC (Credit Card Customer)

## Objective

Implement virtual card creation functionality for Credit Card Customers (CCC) that integrates with customer-master to fetch RLOC (Revolving Line of Credit) account information and handles the complete card creation lifecycle including success, timeout, and error scenarios with appropriate retry mechanisms.

## Flow Analysis

### High-Level Flow
1. **User Initiation**: User provides preferred name and confirms intent to create virtual card
2. **Validation**: System validates user hasn't reached card limit and input parameters
3. **RLOC Integration**: Fetch customer RLOC account information from account service 
4. **Card Creation Request**: Send cleaned-up card creation request to external service (EN)
5. **Response Handling**: Process EN response with timeout and retry logic
6. **Success Path**: Store card details, increment count, set limits, send notifications
7. **Failure Path**: Handle retryable/non-retryable errors with appropriate user feedback

### Detailed State Flow
```
[User Request] → [Validation] → [RLOC Fetch] → [EN Request] → [Response Processing]
                                                                      ↓
[Success] → [Store Card] → [Set Limits] → [Increment Count] → [Notify User]
     ↓
[Retryable Error] → [PROCESSING Status] → [Retry Logic] → [Final Resolution]
     ↓
[Non-Retryable Error] → [FAILED Status] → [Error Notification]
```

## Proposed Changes

### 1. Enhanced Existing API Endpoint
- **Endpoint**: `POST /v1/card` (existing endpoint)
- **Purpose**: Enhanced to support CCC virtual card creation through product variant detection
- **Request Payload**: No changes to existing `CreateCardRequest`
- **Response Payload**: No changes to existing `CardResponse`
- **Product Differentiation**: Automatic detection via account service `ProductVariantID`

### 2. Modular Architecture Implementation

#### Strategy Pattern for Product-Specific Logic
- **Product Strategy Interface**: Define common operations for all product variants
- **Concrete Strategies**: Implement specific behaviors for `MYDebitCard` and `MYCreditCard`
- **Strategy Factory**: Create appropriate strategy based on product variant
- **Configuration-Driven**: Leverage existing `ProductConfigs` map for product-specific settings

#### Service Layer Abstraction
- **Account Verification Strategy**: Product-specific account validation logic
- **Risk Assessment Strategy**: Product-specific risk check parameters
- **Limit Management Strategy**: Product-specific default transaction limits
- **Card Issuance Strategy**: Product-specific vendor parameters

### 3. Database Changes

#### Leverage Existing Card Table Structure
- Use existing `product_variant` field with existing `MYCreditCard` constant ("commercial_credit_card")
- Store RLOC account ID in existing `account_id` field (already used for account association)
- Use existing `reference_id` field for idempotency tracking
- **Reuse existing `status` field** with standard card statuses: "PROCESSING", "ACTIVE", "FAILED"
- Store retry metadata in existing `post_script` JSON column

#### No New Tables, Statuses, or Constants Required
The existing `card` table structure and constants already support all CCC requirements:
- Product differentiation via existing `MYCreditCard` product variant
- Account association via `account_id` column
- Standard status tracking via existing `status` values (PROCESSING, ACTIVE, FAILED)
- Idempotency via `reference_id` column
- Retry metadata via `post_script` JSON column

## Affected Files & Modules

### Modified Files
- `digicard-core/logic/card/create/create_card.go` - Enhanced workflow initialization
- `digicard-core/logic/card/create/workflow.go` - Enhanced ExecutionData structure
- `digicard-core/logic/card/create/account_verify.go` - Strategy pattern integration
- `digicard-core/logic/card/create/risk_check.go` - Strategy pattern integration
- `digicard-core/logic/card/create/card_issuance.go` - Strategy pattern integration

### New Files & Modules
- `digicard-core/logic/card/create/strategy/product_strategy.go` - Strategy interface
- `digicard-core/logic/card/create/strategy/debit_strategy.go` - Debit card strategy
- `digicard-core/logic/card/create/strategy/credit_strategy.go` - Credit card strategy
- `digicard-core/logic/card/create/strategy/factory.go` - Strategy factory
- `digicard-core/logic/card/create/product_config_helper.go` - Configuration utilities

## Architecture Benefits

### Strategy Pattern Advantages
- **Extensibility**: Easy to add new product variants without modifying existing code
- **Maintainability**: Product-specific logic is encapsulated in dedicated strategy classes
- **Testability**: Each strategy can be unit tested independently
- **Single Responsibility**: Each strategy handles one product variant's behavior

### Configuration-Driven Approach
- **Centralized Configuration**: All product-specific settings in `ProductConfigs` map
- **No Hard-Coded Logic**: Product behaviors driven by configuration
- **Runtime Flexibility**: Easy to modify product behaviors via configuration changes
- **Consistency**: Uniform approach to handling different product variants

### Backward Compatibility
- **Zero Breaking Changes**: Existing debit card flow remains completely unchanged
- **Transparent Enhancement**: CCC functionality added without affecting existing APIs
- **Gradual Migration**: Can be deployed incrementally without service disruption
- **Fallback Mechanisms**: Default to existing behavior if strategy not found
