// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"

	mock "github.com/stretchr/testify/mock"
)

// DigicardCore is an autogenerated mock type for the DigicardCore type
type DigicardCore struct {
	mock.Mock
}

// AcceptCardsTerms provides a mock function with given fields: ctx
func (_m *DigicardCore) AcceptCardsTerms(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for AcceptCardsTerms")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AccountStatusEnquiry provides a mock function with given fields: ctx, req
func (_m *DigicardCore) AccountStatusEnquiry(ctx context.Context, req *api.AccountStatusEnquiryRequest) (*api.AccountStatusEnquiryResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for AccountStatusEnquiry")
	}

	var r0 *api.AccountStatusEnquiryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.AccountStatusEnquiryRequest) (*api.AccountStatusEnquiryResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.AccountStatusEnquiryRequest) *api.AccountStatusEnquiryResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.AccountStatusEnquiryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.AccountStatusEnquiryRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BankBlock provides a mock function with given fields: ctx, req
func (_m *DigicardCore) BankBlock(ctx context.Context, req *api.BankBlockRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for BankBlock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.BankBlockRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BankUnblock provides a mock function with given fields: ctx, req
func (_m *DigicardCore) BankUnblock(ctx context.Context, req *api.BankUnblockRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for BankUnblock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.BankUnblockRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CancelCard provides a mock function with given fields: ctx, req
func (_m *DigicardCore) CancelCard(ctx context.Context, req *api.CancelCardRequest) (*api.CardResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CancelCard")
	}

	var r0 *api.CardResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CancelCardRequest) (*api.CardResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CancelCardRequest) *api.CardResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CancelCardRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CancelCardInternal provides a mock function with given fields: ctx, req
func (_m *DigicardCore) CancelCardInternal(ctx context.Context, req *api.CancelCardInternalRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CancelCardInternal")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CancelCardInternalRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CardActivateAndSetPin provides a mock function with given fields: ctx, req
func (_m *DigicardCore) CardActivateAndSetPin(ctx context.Context, req *api.CardActivateAndSetPinRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CardActivateAndSetPin")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CardActivateAndSetPinRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CardNameCheck provides a mock function with given fields: ctx, req
func (_m *DigicardCore) CardNameCheck(ctx context.Context, req *api.CardNameCheckRequest) (*api.CardNameCheckResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CardNameCheck")
	}

	var r0 *api.CardNameCheckResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CardNameCheckRequest) (*api.CardNameCheckResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CardNameCheckRequest) *api.CardNameCheckResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardNameCheckResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CardNameCheckRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CardSetPin provides a mock function with given fields: ctx, req
func (_m *DigicardCore) CardSetPin(ctx context.Context, req *api.CardSetPinRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CardSetPin")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CardSetPinRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateCard provides a mock function with given fields: ctx, req
func (_m *DigicardCore) CreateCard(ctx context.Context, req *api.CreateCardRequest) (*api.CardResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateCard")
	}

	var r0 *api.CardResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateCardRequest) (*api.CardResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateCardRequest) *api.CardResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateCardRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateCheckoutPreviewCardByCardVariantID provides a mock function with given fields: ctx, req
func (_m *DigicardCore) CreateCheckoutPreviewCardByCardVariantID(ctx context.Context, req *api.CreateCheckoutPreviewCardByCardVariantIDRequest) (*api.CreateCheckoutPreviewCardByCardVariantIDResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateCheckoutPreviewCardByCardVariantID")
	}

	var r0 *api.CreateCheckoutPreviewCardByCardVariantIDResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateCheckoutPreviewCardByCardVariantIDRequest) (*api.CreateCheckoutPreviewCardByCardVariantIDResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.CreateCheckoutPreviewCardByCardVariantIDRequest) *api.CreateCheckoutPreviewCardByCardVariantIDResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CreateCheckoutPreviewCardByCardVariantIDResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.CreateCheckoutPreviewCardByCardVariantIDRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchCardLimits provides a mock function with given fields: ctx, req
func (_m *DigicardCore) FetchCardLimits(ctx context.Context, req *api.FetchCardLimitsRequest) (*api.FetchCardLimitsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for FetchCardLimits")
	}

	var r0 *api.FetchCardLimitsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.FetchCardLimitsRequest) (*api.FetchCardLimitsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.FetchCardLimitsRequest) *api.FetchCardLimitsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.FetchCardLimitsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.FetchCardLimitsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FreezeCardInternal provides a mock function with given fields: ctx, req
func (_m *DigicardCore) FreezeCardInternal(ctx context.Context, req *api.FreezeCardInternalRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for FreezeCardInternal")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.FreezeCardInternalRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAccountStatus provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetAccountStatus(ctx context.Context, req *api.AccountStatusEnquiryRequest) (*api.AccountStatusEnquiryResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountStatus")
	}

	var r0 *api.AccountStatusEnquiryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.AccountStatusEnquiryRequest) (*api.AccountStatusEnquiryResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.AccountStatusEnquiryRequest) *api.AccountStatusEnquiryResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.AccountStatusEnquiryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.AccountStatusEnquiryRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllCardDesigns provides a mock function with given fields: ctx
func (_m *DigicardCore) GetAllCardDesigns(ctx context.Context) (*api.GetAllCardDesignsResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllCardDesigns")
	}

	var r0 *api.GetAllCardDesignsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.GetAllCardDesignsResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.GetAllCardDesignsResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetAllCardDesignsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllCards provides a mock function with given fields: ctx
func (_m *DigicardCore) GetAllCards(ctx context.Context) (*api.GetAllCardsResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllCards")
	}

	var r0 *api.GetAllCardsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.GetAllCardsResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.GetAllCardsResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetAllCardsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCardByInternalCardID provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetCardByInternalCardID(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCardByInternalCardID")
	}

	var r0 *api.CardDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSpecificCardRequest) (*api.CardDetail, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSpecificCardRequest) *api.CardDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetSpecificCardRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCardByVendorCardID provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetCardByVendorCardID(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCardByVendorCardID")
	}

	var r0 *api.CardDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSpecificCardRequest) (*api.CardDetail, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSpecificCardRequest) *api.CardDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetSpecificCardRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCardDetailsByCustomer provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetCardDetailsByCustomer(ctx context.Context, req *api.GetCardDetailsRequest) (*api.GetCardDetailsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCardDetailsByCustomer")
	}

	var r0 *api.GetCardDetailsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardDetailsRequest) (*api.GetCardDetailsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardDetailsRequest) *api.GetCardDetailsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCardDetailsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCardDetailsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCardDetailsWithEncryption provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetCardDetailsWithEncryption(ctx context.Context, req *api.GetCardDetailsWithEncryptionRequest) (*api.GetCardDetailsWithEncryptionResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCardDetailsWithEncryption")
	}

	var r0 *api.GetCardDetailsWithEncryptionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardDetailsWithEncryptionRequest) (*api.GetCardDetailsWithEncryptionResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardDetailsWithEncryptionRequest) *api.GetCardDetailsWithEncryptionResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCardDetailsWithEncryptionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCardDetailsWithEncryptionRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCardLimits provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetCardLimits(ctx context.Context, req *api.GetCardLimitRequest) (*api.GetCardLimitsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCardLimits")
	}

	var r0 *api.GetCardLimitsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardLimitRequest) (*api.GetCardLimitsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardLimitRequest) *api.GetCardLimitsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCardLimitsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCardLimitRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCardPinEncryptionKey provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetCardPinEncryptionKey(ctx context.Context, req *api.GetCardPinEncryptionKeyRequest) (*api.GetCardPinEncryptionKeyResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCardPinEncryptionKey")
	}

	var r0 *api.GetCardPinEncryptionKeyResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardPinEncryptionKeyRequest) (*api.GetCardPinEncryptionKeyResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetCardPinEncryptionKeyRequest) *api.GetCardPinEncryptionKeyResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetCardPinEncryptionKeyResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetCardPinEncryptionKeyRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeatureFlags provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetFeatureFlags(ctx context.Context, req *api.GetFeatureFlagsRequest) (*api.GetFeatureFlagsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetFeatureFlags")
	}

	var r0 *api.GetFeatureFlagsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetFeatureFlagsRequest) (*api.GetFeatureFlagsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetFeatureFlagsRequest) *api.GetFeatureFlagsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetFeatureFlagsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetFeatureFlagsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPhysicalCardInventory provides a mock function with given fields: ctx
func (_m *DigicardCore) GetPhysicalCardInventory(ctx context.Context) (*api.GetPhysicalCardInventoryResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetPhysicalCardInventory")
	}

	var r0 *api.GetPhysicalCardInventoryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*api.GetPhysicalCardInventoryResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *api.GetPhysicalCardInventoryResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GetPhysicalCardInventoryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSpecificCard provides a mock function with given fields: ctx, req
func (_m *DigicardCore) GetSpecificCard(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetSpecificCard")
	}

	var r0 *api.CardDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSpecificCardRequest) (*api.CardDetail, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GetSpecificCardRequest) *api.CardDetail); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GetSpecificCardRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OverwritePhysicalCardInventory provides a mock function with given fields: ctx, req
func (_m *DigicardCore) OverwritePhysicalCardInventory(ctx context.Context, req *api.OverwritePhysicalCardInventoryRequest) (*api.OverwritePhysicalCardInventoryResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for OverwritePhysicalCardInventory")
	}

	var r0 *api.OverwritePhysicalCardInventoryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.OverwritePhysicalCardInventoryRequest) (*api.OverwritePhysicalCardInventoryResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.OverwritePhysicalCardInventoryRequest) *api.OverwritePhysicalCardInventoryResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.OverwritePhysicalCardInventoryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.OverwritePhysicalCardInventoryRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PatchUpdateUserMailingAddress provides a mock function with given fields: ctx, req
func (_m *DigicardCore) PatchUpdateUserMailingAddress(ctx context.Context, req *api.PatchUpdateUserMailingAddressRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PatchUpdateUserMailingAddress")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PatchUpdateUserMailingAddressRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PhysicalCardActivation provides a mock function with given fields: ctx, req
func (_m *DigicardCore) PhysicalCardActivation(ctx context.Context, req *api.PhysicalCardActivationRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PhysicalCardActivation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PhysicalCardActivationRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TransactionDisputeCheck provides a mock function with given fields: ctx, req
func (_m *DigicardCore) TransactionDisputeCheck(ctx context.Context, req *api.TransactionDisputeCheckRequest) (*api.TransactionDisputeCheckResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for TransactionDisputeCheck")
	}

	var r0 *api.TransactionDisputeCheckResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.TransactionDisputeCheckRequest) (*api.TransactionDisputeCheckResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.TransactionDisputeCheckRequest) *api.TransactionDisputeCheckResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.TransactionDisputeCheckResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.TransactionDisputeCheckRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TriggerOtp provides a mock function with given fields: ctx, req
func (_m *DigicardCore) TriggerOtp(ctx context.Context, req *api.TriggerOtpRequest) (*api.TriggerOtpResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for TriggerOtp")
	}

	var r0 *api.TriggerOtpResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.TriggerOtpRequest) (*api.TriggerOtpResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.TriggerOtpRequest) *api.TriggerOtpResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.TriggerOtpResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.TriggerOtpRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UnfreezeCardInternal provides a mock function with given fields: ctx, req
func (_m *DigicardCore) UnfreezeCardInternal(ctx context.Context, req *api.UnfreezeCardInternalRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UnfreezeCardInternal")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UnfreezeCardInternalRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCardStatus provides a mock function with given fields: ctx, req
func (_m *DigicardCore) UpdateCardStatus(ctx context.Context, req *api.UpdateCardRequest) (*api.CardResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCardStatus")
	}

	var r0 *api.CardResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateCardRequest) (*api.CardResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.UpdateCardRequest) *api.CardResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.UpdateCardRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateCnpToggle provides a mock function with given fields: ctx, req
func (_m *DigicardCore) UpdateCnpToggle(ctx context.Context, req *api.ToggleUpdateRequest) (*api.ToggleUpdateResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCnpToggle")
	}

	var r0 *api.ToggleUpdateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ToggleUpdateRequest) (*api.ToggleUpdateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ToggleUpdateRequest) *api.ToggleUpdateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ToggleUpdateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ToggleUpdateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateCnpToggleInternal provides a mock function with given fields: ctx, req
func (_m *DigicardCore) UpdateCnpToggleInternal(ctx context.Context, req *api.InternalToggleUpdateRequest) (*api.InternalToggleUpdateResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCnpToggleInternal")
	}

	var r0 *api.InternalToggleUpdateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.InternalToggleUpdateRequest) (*api.InternalToggleUpdateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.InternalToggleUpdateRequest) *api.InternalToggleUpdateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.InternalToggleUpdateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.InternalToggleUpdateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateOverseasToggle provides a mock function with given fields: ctx, req
func (_m *DigicardCore) UpdateOverseasToggle(ctx context.Context, req *api.ToggleUpdateRequest) (*api.ToggleUpdateResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOverseasToggle")
	}

	var r0 *api.ToggleUpdateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.ToggleUpdateRequest) (*api.ToggleUpdateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.ToggleUpdateRequest) *api.ToggleUpdateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.ToggleUpdateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.ToggleUpdateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateOverseasToggleInternal provides a mock function with given fields: ctx, req
func (_m *DigicardCore) UpdateOverseasToggleInternal(ctx context.Context, req *api.InternalToggleUpdateRequest) (*api.InternalToggleUpdateResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOverseasToggleInternal")
	}

	var r0 *api.InternalToggleUpdateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.InternalToggleUpdateRequest) (*api.InternalToggleUpdateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.InternalToggleUpdateRequest) *api.InternalToggleUpdateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.InternalToggleUpdateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.InternalToggleUpdateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewDigicardCore creates a new instance of DigicardCore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDigicardCore(t interface {
	mock.TestingT
	Cleanup(func())
}) *DigicardCore {
	mock := &DigicardCore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
