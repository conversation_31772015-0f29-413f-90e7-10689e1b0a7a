package api_test

import (
	"testing"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/whitelist"

	digicardTxnMock "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api/mock"
	euronetAdapterMock "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api/mock"
	riskMock "gitlab.myteksi.net/dakota/client-sdk/risk-service"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/servustest"
	customerMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2/inject"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	transactionLimitMock "gitlab.myteksi.net/dakota/transaction-limit/api/mock"
	weMock "gitlab.myteksi.net/dakota/workflowengine/storage/mocks"
	accountMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
	pigeon "gitlab.myteksi.net/dbmy/pigeon/api/mock"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/external/hedwig"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/handlers"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/acs/otp"
	mailingaddress "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/address/mailing"
	digiCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/activate"
	activeandsetpin "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/active_and_set_pin"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/block"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/cnp"
	create "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/create"
	getalldesigns "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/get_all_designs"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	updateCardStock "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/update_card_stock"
	getallcards "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_all_cards"
	getCardDetailsWithEncryption "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_card_details_with_encryption"
	getCardsByCustomer "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_cards_by_customer"
	getSpecificCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_specific_card"
	pinmanager "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/pin_manager"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/toggle"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/unblock"
	update "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/update"
	updatecardstatus "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/update_card_status"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/enquiry"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/notification"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/terms"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
)

const (
	randomID           = "1234"
	randomName         = "ABC"
	sampleGenericError = `{
								"code": "INTERNAL_SERVER_ERROR",
								"message": "There is a problem on our end. Please try again later."
							}`
)

var (
	service                *handlers.DigicardCoreService
	mockCardDAO            *storage.MockICardDAO
	mockAccountService     accountMock.AccountService
	mockCustomerMaster     customerMock.CustomerMaster
	mockCustomerMasterDBMY customerDBMYMock.CustomerMaster
	mockRiskService        riskMock.MockRiskService
	mockEuronetAdapter     euronetAdapterMock.EuronetAdapter
	mockWorkflowDAO        weMock.IWorkflowExecutionDAO
	mockServiceConfig      config.ServiceConfig
	mockAppConfig          *config.AppConfig
	mockPigeon             pigeon.Pigeon
	mockHedwig             hedwig.MockNotifier
	mockCardDesignDAO      *storage.MockICardDesignDAO

	server servustest.ServerDescriptor

	createWorkflow   *create.WorkflowImpl
	updateWorkflow   *update.WorkflowImpl
	activateWorkflow *activeandsetpin.WorkflowImpl

	mockRedis                   *mocks.Client
	mockTransactionLimitClient  transactionLimitMock.TransactionLimit
	limitDetail                 *config.LimitConfig
	mockToggleUpdate            *toggle.MockClient
	mockMailingAddressClient    *mailingaddress.MockClient
	mockDigicardTXNClient       digicardTxnMock.DigicardTransaction
	mockPhysicalCardActivate    *activate.MockPhysicalCardActivateClient
	mockKafkaWriter             *kafkawriter.MockClient
	mockPublisher               *digiCard.PublisherImpl
	mockBankBlockClient         *block.MockBankBlockClient
	mockCardsTermsClient        *terms.MockClient
	mockBankUnblockClient       *unblock.MockBankUnblockClient
	mockUpdateCardStatusClient  *updatecardstatus.MockClient
	mockPhysicalCardOrderClient *create.MockPhysicalCardOrderClient
	mockAccountVerifyClient     *create.MockAccountVerifyClient
	mockCNP                     *cnp.MockClient
	mockPinManagerClient        *pinmanager.MockCardPinManagerClient
	mockWhitelistClient         *whitelist.MockClient
	cardAuditLog                *audit.MockCardActivityLogService
	mockAccountServiceClient    *accountMock.AccountService
)

func TestApi(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "API Test Suite")
}

var _ = BeforeSuite(func() {
	limitDetail = &config.LimitConfig{
		Name:                "DIGICARD_DEBITCARD_LIMIT",
		Title:               "Set daily card limit",
		SubTitle:            "Set daily card limit",
		PreferredCardLimits: []int64{1000, 5000, 10000},
	}

	mockAppConfig = &config.AppConfig{}
	featureFlags := &config.FeatureFlags{
		EnableAccountStatusEnquiryCheck:                   false,
		EnableCardCreationPermissionAndAccountStatusCheck: true,
		EnableNewCardUpdateStatusFlow:                     false,
	}
	workFlowRetry := &config.WorkflowRetryConfig{VirtualCardCreation: &config.RetryPolicy{
		IntervalInSeconds: 60,
		MaxAttempt:        5,
	}}

	mockKafkaWriter = &kafkawriter.MockClient{}
	mockPublisher = &digiCard.PublisherImpl{
		KafkaWriter: mockKafkaWriter,
	}

	cardAuditLog = &audit.MockCardActivityLogService{}

	injector := inject.New()
	injector.MustRegister("config.timeoutConfig", config.TimeoutConfig{})
	injector.MustRegister("config.ServiceConfig", mockServiceConfig)
	injector.MustRegister("config.AppConfig", mockAppConfig)
	injector.MustRegister("config.featureFlags", featureFlags)
	injector.MustRegister("config.limits", []config.LimitConfig{*limitDetail})
	injector.MustRegister("config.physicalCardDelivery", config.PhysicalCardDeliveryConfig{
		OrderConfirmed:    1,
		Posted:            13,
		NotReceivedYet:    14,
		CanActivateCard:   2,
		CanViewHelpCenter: 14,
	})
	injector.MustRegister("writer.daStream", mockKafkaWriter)
	injector.MustRegister("client.activityLog", cardAuditLog)
	injector.MustRegister("config.workflowRetry", workFlowRetry)
	injector.MustRegister("config.cardIssuanceConfig", config.CardIssuanceConfig{
		MaxAllowedActiveCards: 1,
		MaxAllowedCards:       1,
	})
	injector.MustRegister("config.physicalCardOrderFee", config.PhysicalCardOrderFee{
		Amount: 1200,
	})
	injector.MustRegister("config.countryConfig", config.CountryConfig{})

	prepareDependencies(injector)
	prepareModules(injector)

	service = &handlers.DigicardCoreService{}
	injector.MustRegister("service", service)
	server = servustest.StartServer(service)
})

var _ = AfterSuite(func() {
	server.Shutdown()
})

func prepareDependencies(injector *inject.Container) {
	injector.MustRegister("statsD", statsd.NewNoop)
	injector.MustRegister("storage.card", &mockCardDAO)
	injector.MustRegister("storage.cardDesign", storage.CardDesignDao)
	injector.MustRegister("client.accountServiceV2", &mockAccountService)
	injector.MustRegister("client.customerMasterDBMY", &mockCustomerMasterDBMY)
	injector.MustRegister("client.riskService", &mockRiskService)
	injector.MustRegister("client.customerMaster", &mockCustomerMaster)
	injector.MustRegister("client.euronetAdapter", &mockEuronetAdapter)
	injector.MustRegister("client.hedwig", &mockHedwig)
	injector.MustRegister("client.pigeon", &mockPigeon)
	injector.MustRegister("client.transactionLimit", &mockTransactionLimitClient)
	injector.MustRegister("client.digicardTxnClient", &mockDigicardTXNClient)
	injector.MustRegister("publisher.card", &mockPublisher)
	injector.MustRegister("notifier.pushNotifier", &notification.PushNotifier{})
	injector.MustRegister("notifier.emailNotifier", &notification.EmailNotifier{})
	mockCardDesignDAO = test.SetCardDesignMockDao()
	mockRedis = test.SetMockRedisClient()
}

func prepareModules(injector *inject.Container) {
	getAllCards := getallcards.NewClient()
	getSpeciFicCard := getSpecificCard.NewClient()
	getCardDetailsWithEncryptionClient := getCardDetailsWithEncryption.NewClient()
	statusEnquiry := enquiry.NewClient()
	acsTriggerOtp := otp.NewClient()
	getAllCardDesigns := getalldesigns.NewClient()
	mockGetCardsByCustomer := getCardsByCustomer.NewClient()
	mockPhysicalCardActivate = &activate.MockPhysicalCardActivateClient{}
	createWorkflow = &create.WorkflowImpl{}
	updateWorkflow = &update.WorkflowImpl{}
	activateWorkflow = &activeandsetpin.WorkflowImpl{}
	mockUpdateCardStock := updateCardStock.NewClient()
	mockToggleUpdate = &toggle.MockClient{}
	mockMailingAddressClient = &mailingaddress.MockClient{}
	mockDisputeCheckCLient := transaction.NewClient()

	mockBankBlockClient = &block.MockBankBlockClient{}
	mockBankUnblockClient = &unblock.MockBankUnblockClient{}
	mockCardsTermsClient = &terms.MockClient{}
	mockUpdateCardStatusClient = &updatecardstatus.MockClient{}
	mockPhysicalCardOrderClient = &create.MockPhysicalCardOrderClient{}
	mockAccountVerifyClient = &create.MockAccountVerifyClient{}
	mockWhitelistClient = &whitelist.MockClient{}

	mockCNP = &cnp.MockClient{}
	mockPinManagerClient = &pinmanager.MockCardPinManagerClient{}

	injector.MustRegister("createCardWorkflow", createWorkflow)
	injector.MustRegister("updateCardWorkflow", updateWorkflow)
	injector.MustRegister("client.GetAllCards", getAllCards)
	injector.MustRegister("client.GetSpecificCard", getSpeciFicCard)
	injector.MustRegister("client.GetCardDetailsWithEncryption", getCardDetailsWithEncryptionClient)
	injector.MustRegister("client.Enquiry", statusEnquiry)
	injector.MustRegister("client.acsTriggerOtp", acsTriggerOtp)
	injector.MustRegister("client.GetAllCardDesigns", getAllCardDesigns)
	injector.MustRegister("client.GetCardsByCustomer", mockGetCardsByCustomer)
	injector.MustRegister("client.updateCardStock", mockUpdateCardStock)
	injector.MustRegister("client.toggleUpdate", mockToggleUpdate)
	injector.MustRegister("client.disputeCheck", mockDisputeCheckCLient)
	injector.MustRegister("client.mailingAddress", mockMailingAddressClient)
	injector.MustRegister("client.physicalCardActivate", mockPhysicalCardActivate)
	injector.MustRegister("client.bankBlock", mockBankBlockClient)
	injector.MustRegister("client.bankUnblock", mockBankUnblockClient)
	injector.MustRegister("client.cardsTerms", mockCardsTermsClient)
	injector.MustRegister("client.updateCardStatus", mockUpdateCardStatusClient)
	injector.MustRegister("client.physicalCardOrder", mockPhysicalCardOrderClient)
	injector.MustRegister("client.accountVerify", mockAccountVerifyClient)
	injector.MustRegister("client.cnpToggleUpdate", mockCNP)
	injector.MustRegister("client.cardPinManager", mockPinManagerClient)
	injector.MustRegister("activeAndSetPinWorkflow", activateWorkflow)
	injector.MustRegister("client.whitelist", mockWhitelistClient)

	createWorkflow.RegisterWorkflow()
	updateWorkflow.RegisterWorkflow()
	activateWorkflow.RegisterWorkflow()
}

const (
	invalidCustomerErrResponse = `{
								      "code": "BAD_REQUEST",
									  "message": "request has invalid parameter(s) or header(s).",
								      "errors": [
              							{
                							"errorCode": "FIELD_INVALID",
                							"message": "string cannot be empty",
                							"path": "CustomerID"
              							}
									 ]
							      }`
)
