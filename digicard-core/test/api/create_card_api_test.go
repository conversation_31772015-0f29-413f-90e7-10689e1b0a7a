package api_test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	accountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.myteksi.net/dakota/common/servicename"

	"github.com/google/uuid"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	riskservice "gitlab.myteksi.net/dakota/client-sdk/risk-service"
	ctx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/workflowengine"
	weStorage "gitlab.myteksi.net/dakota/workflowengine/storage"
	customerMasterAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"

	create "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/create"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
)

var _ = Describe("Create", func() {
	var (
		client   *hcl.Client
		setupErr error
	)

	BeforeEach(func() {
		client, setupErr = hcl.NewClient(server.URL())
		Expect(setupErr).ShouldNot(HaveOccurred())
		mockCardDAO = &storage.MockICardDAO{}
		storage.CardDao = mockCardDAO
		test.SetMockDataForRedisClient(mockRedis)
		test.SetMockDataForCardDesign(mockCardDesignDAO)
		cardAuditLog.On("SendActivityLog", mock.Anything, mock.Anything).Twice()
	})

	Describe("Calling create card API with appropriate parameters", func() {
		apiURL := "/v1/card"
		testAccountID := uuid.NewString()
		testDisplayName := "ABC"
		testUserID := uuid.NewString()
		testIdempotencyKey := uuid.NewString()
		testCardID := uuid.NewString()
		testCardDesignID := test.TestCardDesignID
		testUnavailableCardDesignID := test.TestUnavailableCardDesignID

		Context("Invalid Request Parameters", func() {
			When("User ID is missing in the header", func() {
				requestBody := dummyRequest(testAccountID, testDisplayName, string(constant.VirtualCard), testIdempotencyKey, "", testCardDesignID)
				userIDModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, ""),
					hcl.JSON(requestBody),
				}
				userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
				It("Return 403 Forbidden", func() {
					resp, err := client.Post(apiURL, userIDModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(403))
					expectedResponse := `{
												  "code": "FORBIDDEN",
												  "message": "'X-Grab-Id-Userid' is missing."
												}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("Idempotency key is missing in the body", func() {
				requestBody := dummyRequest(testAccountID, testDisplayName, string(constant.VirtualCard), "", "", testCardDesignID)
				userIDModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
				It("Returns 400", func() {
					resp, err := client.Post(apiURL, userIDModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
														  "code": "BAD_REQUEST",
														  "message": "request has invalid parameter(s) or header(s).",
														  "errors": [
															{
															  "errorCode": "FIELD_MISSING",
															  "message": "'idempotencyKey' is missing",
															  "path": "idempotencyKey"
															}
														  ]
														}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("user is not whitelisted", func() {
				requestBody := dummyRequest(testAccountID, testDisplayName, string(constant.VirtualCard), testIdempotencyKey, "", testCardDesignID)
				userIDModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, "abcd"),
					hcl.JSON(requestBody),
				}
				userIDModifier = injectIdentityHeader(userIDModifier, servicename.SentryT6)
				It("Return 403 Forbidden", func() {
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(false).Once()
					resp, err := client.Post(apiURL, userIDModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(403))
					expectedResponse := `{
												  "code": "FORBIDDEN",
												  "message": "user is not whitelisted to create card"
												}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("accountID is missing in the body", func() {
				requestBody := dummyRequest("", testDisplayName, string(constant.VirtualCard), testIdempotencyKey, "", testCardDesignID)
				requestModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
														  "code": "BAD_REQUEST",
														  "message": "request has invalid parameter(s) or header(s).",
														  "errors": [
															{
															  "errorCode": "FIELD_MISSING",
															  "message": "'accountID' is missing",
															  "path": "accountID"
															}
														  ]
														}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("type is missing in the body", func() {
				requestBody := dummyRequest(testAccountID, testDisplayName, "", testIdempotencyKey, "", testCardDesignID)
				requestModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
														  "code": "BAD_REQUEST",
														  "message": "request has invalid parameter(s) or header(s).",
														  "errors": [
															{
															  "errorCode": "FIELD_INVALID",
															  "message": "invalid Type",
															  "path": "type"
															}
														  ]
														}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("displayName is missing in the body", func() {
				requestBody := dummyRequest(testAccountID, "", string(constant.VirtualCard), testIdempotencyKey, "", testCardDesignID)
				requestModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
														  "code": "BAD_REQUEST",
														  "message": "request has invalid parameter(s) or header(s).",
														  "errors": [
															{
															  "errorCode": "FIELD_MISSING",
															  "message": "'displayName' is missing",
															  "path": "displayName"
															}
														  ]
														}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("cardID missing for Physical card", func() {
				requestBody := dummyRequest(testAccountID, testDisplayName, string(constant.PhysicalCard), testIdempotencyKey, "", testCardDesignID)
				requestModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
														  "code": "BAD_REQUEST",
														  "message": "request has invalid parameter(s) or header(s).",
														  "errors": [
															{
															  "errorCode": "FIELD_MISSING",
															  "message": "'cardID' is missing",
															  "path": "cardID"
															}
														  ]
														}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("Invalid Type", func() {
				requestBody := dummyRequest(testAccountID, testDisplayName, "random", testIdempotencyKey, "123", testCardDesignID)
				requestModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
														  "code": "BAD_REQUEST",
														  "message": "request has invalid parameter(s) or header(s).",
														  "errors": [
															{
															  "errorCode": "FIELD_INVALID",
															  "message": "invalid Type",
															  "path": "type"
															}
														  ]
														}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("Invalid Display Name", func() {
				requestBody := dummyRequest(testAccountID, "a1b2", string(constant.VirtualCard), testIdempotencyKey, "", testCardDesignID)
				requestModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
														  "code": "BAD_REQUEST",
														  "message": "request has invalid parameter(s) or header(s).",
														  "errors": [
															{
															  "errorCode": "FIELD_INVALID",
															  "message": "invalid Display Name",
															  "path": "displayName"
															}
														  ]
														}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("Card Design Id is missing", func() {
				requestBody := dummyRequest(testAccountID, testDisplayName, string(constant.VirtualCard), testIdempotencyKey, "", "")
				requestModifier := []hcl.RequestModifier{
					hcl.Header(ctx.HeaderXUserID, testUserID),
					hcl.JSON(requestBody),
				}
				requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
				It("Return 409", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
							"code": "BAD_REQUEST",
							"message": "request has invalid parameter(s) or header(s).",
							"errors": [
								{
									"errorCode": "FIELD_MISSING",
									"message": "'cardDesignID' is missing",
									"path": "cardDesignID"
								}
							]
						}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
		})

		Context("create card request with all parameters present", func() {
			defaultBody := dummyRequest(testAccountID, testDisplayName, string(constant.VirtualCard), testIdempotencyKey, "", testCardDesignID)
			defaultModifier := []hcl.RequestModifier{
				hcl.Header(ctx.HeaderXUserID, testUserID),
				hcl.JSON(defaultBody),
			}
			defaultModifier = injectIdentityHeader(defaultModifier, servicename.SentryT6)
			When("Invalid AccountID", func() {
				It("Return 400", func() {
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					expectedErr := servus.ServiceError{
						Code:     string(api.Forbidden),
						Message:  "the operation is forbidden",
						HTTPCode: api.Forbidden.HTTPStatusCode(),
					}
					mockLock := &mocks.Lock{}

					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection (called in handler before error occurs)
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: string(constant.MYDebitCard),
					}}, nil).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(expectedErr).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
						mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(403))
					expectedResponse := `{
											"code": "FORBIDDEN",
											"message": "the operation is forbidden"
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("cannot save condition and term", func() {
				It("return 500", func() {
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					expectedErr := api.DefaultInternalServerError
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(expectedErr).Once()
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(expectedErr.HTTPCode))
				})
			})
			When("create card fail with multiple request - ErrLockOccupied", func() {
				It("returns 400 with status FAILED", func() {
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(nil, redis.ErrLockOccupied).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))

					expectedResponse := `{
												  "code": "TOO_MANY_REQUEST",
												  "message": "Too Many Requests"
												}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("create card fail with  multiple request", func() {
				It("returns 400 with status FAILED", func() {
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(nil, redis.ErrAlreadyExists).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))

					expectedResponse := `{
												  "code": "INTERNAL_SERVER_ERROR",
												  "message": "There is a problem on our end. Please try again later."
												}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("valid flow", func() {
				It("Returns 200 OK", func() {
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockLock := &mocks.Lock{}
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: "1234",
					}}, nil).Twice()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(workflowengine.StateInit, "PROCESSING", testIdempotencyKey, testUserID), nil).Once()
					mockRiskService.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(&riskservice.ValidatePartnerTxnResponse{
						StatusCode: riskservice.Allow,
					})

					mockCardDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(&euronetAdapterAPI.CardIssuanceResponse{
						Data: &euronetAdapterAPI.CardResource{
							Status:             constant.Success,
							StatusReason:       "",
							StatusMessage:      "",
							MaskedCardNumber:   "123456****9102",
							ExpiryDate:         "03/26",
							Cvv:                "233",
							ProxyNumber:        "1234",
							CardSequenceNumber: 0,
						},
					}, nil).Once()
					mockHedwig.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
					customerData := &customerMasterAPI.Customer{
						PreferredName: &testDisplayName,
						Name:          &testDisplayName,
					}
					customerBytes, _ := json.Marshal(customerData)
					testGetCustomerResponse := &customerMasterAPI.GetCustomerResponse{Customer: &customerMasterAPI.CustomerData{Data: customerBytes}}
					accountResponse := &accountServiceAPI.GetCASAAccountResponse{
						Account: &accountServiceAPI.CASAAccount{
							ProductVariantID: "1234",
						},
					}
					mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(testGetCustomerResponse, nil).Once()
					mockPigeon.On("Email", mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{}}, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
						ID:             0,
						UserID:         "",
						CardID:         randomID,
						AccountID:      testAccountID,
						DisplayName:    randomName,
						Status:         string(constant.CardStatusActive),
						HeadCardNumber: "",
						TailCardNumber: "",
						Currency:       "",
						Country:        "",
						Provider:       "",
						VendorID:       "",
						PostScript:     nil,
						ActivatedAt:    time.Now(),
						CreatedAt:      time.Time{},
						UpdatedAt:      time.Time{},
						CardDesignID:   testCardDesignID,
					}}, nil).Once()
					mockAccountService.On("GetCASAAccount", mock.Anything, mock.Anything).Return(accountResponse, nil)
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockKafkaWriter.On("Save", mock.Anything).Return(nil)
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					respObj := &api.CardResponse{}
					_ = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(respObj.Card.Status).Should(Equal(string(constant.CardStatusActive)))
					Expect(respObj.Card.TailCardNumber).Should(Equal("9102"))
					Expect(respObj.Card.HeadCardNumber).Should(Equal("123456"))
					Expect(respObj.Card.DisplayName).Should(Equal("ABC"))
					Expect(respObj.Card.VendorCardID).Should(Equal("1234"))
					Expect(respObj.Card).NotTo(BeNil())
				})
			})
			When("euronet card issuance failed with non-retryable error", func() {
				It("returns 200 with status FAILED", func() {
					errDummy := "dummy error"
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockLock := &mocks.Lock{}
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: string(constant.MYDebitCard),
					}}, nil).Twice()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDesignDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(&[]*storage.CardDesign{{CardDesignID: testCardDesignID}}, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockCardDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(workflowengine.StateInit, "PROCESSING", testIdempotencyKey, testUserID), nil).Once()
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Times(3)
					mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(nil,
						&errorhandling.Error{
							HTTPCode: http.StatusInternalServerError,
							Code:     errDummy,
							Message:  errDummy,
						}).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockHedwig.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockKafkaWriter.On("Save", mock.Anything).Return(nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					theResp := &api.CardResponse{}
					_ = json.Unmarshal(resp.Body.Bytes, theResp)
					expectedResponse := fmt.Sprintf(`{
												"status":"FAILED",
												"statusReason":"%s:%s"
										}`, errDummy, errDummy)
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("euronet card issuance failed with retryable error", func() {
				It("returns 200 with PROCESSING", func() {
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: string(constant.MYDebitCard),
					}}, nil).Twice()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					// Second call
					mockLock := &mocks.Lock{}
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDesignDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(&[]*storage.CardDesign{{CardDesignID: testCardDesignID}}, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockCardDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(workflowengine.StateInit, "PROCESSING", testIdempotencyKey, testUserID), nil).Once()
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Times(3)
					mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(nil,
						&errorhandling.Error{
							HTTPCode: http.StatusInternalServerError,
							Code:     "GENERIC_ERROR",
							Message:  "err={\"code\":\"RETRYABLE_ERROR\",\"message\":\"Something went wrong, please try again later.\"}\n",
						}).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockHedwig.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockKafkaWriter.On("Save", mock.Anything).Return(nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					theResp := &api.CardResponse{}
					_ = json.Unmarshal(resp.Body.Bytes, theResp)
					expectedResponse := `{
												"status":"PROCESSING",
												"statusReason": "error calling euronet issuance API"
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("When card is already available", func() {
				It("Returns 403 Forbidden", func() {
					mockLock := &mocks.Lock{}
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: string(constant.MYDebitCard),
					}}, nil).Twice()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
						ID:             0,
						UserID:         "",
						CardID:         randomID,
						AccountID:      testAccountID,
						DisplayName:    randomName,
						Status:         "ACTIVE",
						HeadCardNumber: "",
						TailCardNumber: "",
						Currency:       "",
						Country:        "",
						Provider:       "",
						VendorID:       "",
						PostScript:     nil,
						ActivatedAt:    time.Now(),
						CreatedAt:      time.Time{},
						UpdatedAt:      time.Time{},
						CardDesignID:   testCardDesignID,
					}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(&euronetAdapterAPI.CardIssuanceResponse{
						Data: &euronetAdapterAPI.CardResource{
							Status:             constant.Success,
							StatusReason:       "",
							StatusMessage:      "",
							MaskedCardNumber:   "123456****9102",
							ExpiryDate:         "03/26",
							Cvv:                "233",
							ProxyNumber:        "1234",
							CardSequenceNumber: 0,
						},
					}, nil).Once()
					mockHedwig.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{Status: "ACTIVE"}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(403))
					expectedResponse := `{
								"code": "EXCEEDED_ALLOWED_ACTIVE_CARDS",
			                 "message": "Exceeded max allowed active cards per user"
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("When no available design", func() {
				It("Returns 200 Ok", func() {
					mockLock := &mocks.Lock{}
					mockCardDesignDAO = test.SetCardDesignMockDao()
					test.SetMockUnavailableDataForCardDesign(mockCardDesignDAO)
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: string(constant.MYDebitCard),
					}}, nil).Twice()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(workflowengine.StateInit, "PROCESSING", testIdempotencyKey, testUserID), nil).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
						ID:             0,
						UserID:         "",
						CardID:         randomID,
						AccountID:      testAccountID,
						DisplayName:    randomName,
						Status:         "",
						HeadCardNumber: "",
						TailCardNumber: "",
						Currency:       "",
						Country:        "",
						Provider:       "",
						VendorID:       "",
						PostScript:     nil,
						ActivatedAt:    time.Now(),
						CreatedAt:      time.Time{},
						UpdatedAt:      time.Time{},
						CardDesignID:   testCardDesignID,
					}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(&euronetAdapterAPI.CardIssuanceResponse{
						Data: &euronetAdapterAPI.CardResource{
							Status:             constant.Success,
							StatusReason:       "",
							StatusMessage:      "",
							MaskedCardNumber:   "123456****9102",
							ExpiryDate:         "03/26",
							Cvv:                "233",
							ProxyNumber:        "1234",
							CardSequenceNumber: 0,
						},
					}, nil).Once()
					mockHedwig.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{Status: "ACTIVE"}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockKafkaWriter.On("Save", mock.Anything).Return(nil).Once()
					customerData := &customerMasterAPI.Customer{
						PreferredName: &testDisplayName,
						Name:          &testDisplayName,
					}
					customerBytes, _ := json.Marshal(customerData)
					testGetCustomerResponse := &customerMasterAPI.GetCustomerResponse{Customer: &customerMasterAPI.CustomerData{Data: customerBytes}}
					mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(testGetCustomerResponse, nil).Once()
					mockPigeon.On("Email", mock.Anything, mock.Anything).Return(nil, nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					respObj := &api.CardResponse{}
					_ = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(respObj.Card.Status).Should(Equal(string(constant.CardStatusActive)))
					Expect(respObj.Card.TailCardNumber).Should(Equal("9102"))
					Expect(respObj.Card.HeadCardNumber).Should(Equal("123456"))
					Expect(respObj.Card.DisplayName).Should(Equal("ABC"))
					Expect(respObj.Card.VendorCardID).Should(Equal("1234"))
					Expect(respObj.Card).NotTo(BeNil())
				})
			})
			When("When fetch available design fails", func() {
				It("Returns 500", func() {
					mockLock := &mocks.Lock{}
					mockCardDesignDAO = test.SetCardDesignMockDao()
					test.SetEmptyMockForCardDesign(mockCardDesignDAO)
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: string(constant.MYDebitCard),
					}}, nil).Twice()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
						ID:             0,
						UserID:         "",
						CardID:         randomID,
						AccountID:      testAccountID,
						DisplayName:    randomName,
						Status:         "",
						HeadCardNumber: "",
						TailCardNumber: "",
						Currency:       "",
						Country:        "",
						Provider:       "",
						VendorID:       "",
						PostScript:     nil,
						ActivatedAt:    time.Now(),
						CreatedAt:      time.Time{},
						UpdatedAt:      time.Time{},
						CardDesignID:   testCardDesignID,
					}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(&euronetAdapterAPI.CardIssuanceResponse{
						Data: &euronetAdapterAPI.CardResource{
							Status:             constant.Success,
							StatusReason:       "",
							StatusMessage:      "",
							MaskedCardNumber:   "123456****9102",
							ExpiryDate:         "03/26",
							Cvv:                "233",
							ProxyNumber:        "1234",
							CardSequenceNumber: 0,
						},
					}, nil).Once()
					mockHedwig.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{Status: "ACTIVE"}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					//mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
				})
			})
			When("When at least one design is available and selected design is not available", func() {
				It("Returns 409", func() {
					defaultBody = dummyRequest(testAccountID, testDisplayName, string(constant.VirtualCard), testIdempotencyKey, "", testUnavailableCardDesignID)
					defaultModifier = []hcl.RequestModifier{
						hcl.Header(ctx.HeaderXUserID, testUserID),
						hcl.JSON(defaultBody),
					}
					defaultModifier = injectIdentityHeader(defaultModifier, servicename.SentryT6)
					mockCardDesignDAO = test.SetCardDesignMockDao()
					test.SetMockDataForCardDesign(mockCardDesignDAO)
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					workflowengine.WithCache(mockRedis)
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					// Mock account service for product variant detection
					mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.MatchedBy(func(req *accountServiceAPI.GetAccountRequest) bool {
						return req.AccountID == testAccountID
					})).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:               "123",
						Status:           accountServiceAPI.AccountStatus_ACTIVE,
						ProductVariantID: string(constant.MYDebitCard),
					}}, nil).Twice()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
						ID:             0,
						UserID:         "",
						CardID:         randomID,
						AccountID:      testAccountID,
						DisplayName:    randomName,
						Status:         "",
						HeadCardNumber: "",
						TailCardNumber: "",
						Currency:       "",
						Country:        "",
						Provider:       "",
						VendorID:       "",
						PostScript:     nil,
						ActivatedAt:    time.Now(),
						CreatedAt:      time.Time{},
						UpdatedAt:      time.Time{},
						CardDesignID:   testUnavailableCardDesignID,
					}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(&euronetAdapterAPI.CardIssuanceResponse{
						Data: &euronetAdapterAPI.CardResource{
							Status:             constant.Success,
							StatusReason:       "",
							StatusMessage:      "",
							MaskedCardNumber:   "123456****9102",
							ExpiryDate:         "03/26",
							Cvv:                "233",
							ProxyNumber:        "1234",
							CardSequenceNumber: 0,
						},
					}, nil).Once()
					mockLock := &mocks.Lock{}
					mockHedwig.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
					mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{Status: "ACTIVE"}}, nil).Once()
					mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					//mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(409))
					expectedResponse := `{
								"code": "RESOURCE_CONFLICT",
			                  "message": "Selected card design is not available."
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
			When("card is bank blocked and virtual card already exists", func() {
				It("Returns 200 and physical card is issued", func() {
					mockLock := &mocks.Lock{}
					requestBody := dummyRequest(testAccountID, testDisplayName, string(constant.PhysicalCard), testIdempotencyKey, testCardID, testCardDesignID)
					requestModifier := []hcl.RequestModifier{
						hcl.Header(ctx.HeaderXUserID, testUserID),
						hcl.JSON(requestBody),
					}
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					requestModifier = injectIdentityHeader(requestModifier, servicename.SentryT6)
					mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()
					mockCardsTermsClient.On("PersistCardsTermsAcceptance", mock.Anything, mock.Anything).Return(nil).Once()
					mockAccountVerifyClient.On("VerifyAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					mockPhysicalCardOrderClient.On("PhysicalCardOrder", mock.Anything, mock.Anything).Return(&api.CardResponse{
						Card: &api.CardDetail{
							CardID:      testCardID,
							DisplayName: testDisplayName,
						},
						Status: constant.Success,
					}, nil).Once()
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					respObj := &api.CardResponse{}
					_ = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(respObj.Status).Should(Equal(constant.Success))
					Expect(respObj.Card.CardID).Should(Equal(testCardID))
					Expect(respObj.Card.DisplayName).Should(Equal(testDisplayName))
					Expect(respObj.Card).NotTo(BeNil())
				})
			})
		})
	})
})

func dummyRequest(accountID string, displayName string, cardType string, idempotencyKey string, cardID string, cardDesignID string) *api.CreateCardRequest {
	return &api.CreateCardRequest{
		AccountID:      accountID,
		DisplayName:    displayName,
		Type:           cardType,
		IdempotencyKey: idempotencyKey,
		CardID:         cardID,
		CardDesignId:   cardDesignID,
	}
}

// nolint
func dummyExecutionData(state workflowengine.State, status string, testIdempotencyKey string, testUserID string) []*weStorage.WorkflowExecution {
	executeData := create.ExecutionData{
		Card: storage.Card{
			CardID:         randomID,
			DisplayName:    randomName,
			CardDesignID:   test.TestCardDesignID,
			PostScript:     []byte(`{"ActivationCode": "J6A7B", "DeliveryAddress": null}`),
			ProductVariant: string(constant.MYDebitCard), // Default to debit for backward compatibility
		},
		State:          state,
		IdempotencyKey: testIdempotencyKey,
		CustomerID:     testUserID,
		Request: &api.CreateCardRequest{
			IdempotencyKey: testIdempotencyKey,
			DisplayName:    randomName,
			Type:           string(constant.VirtualCard),
			AccountID:      randomID,
			CardDesignId:   test.TestCardDesignID,
		},
		Status:         status,
		ProductVariant: string(constant.MYDebitCard),                    // Added: Product variant
		ProductConfig:  constant.GetProductConfig(constant.MYDebitCard), // Added: Product config
	}
	byteData, _ := json.Marshal(executeData)
	return []*weStorage.WorkflowExecution{
		{
			Data:  byteData,
			State: 0,
		},
	}
}
