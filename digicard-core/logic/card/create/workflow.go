package createcard

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/external/hedwig"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/notification"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	riskAPI "gitlab.myteksi.net/dakota/client-sdk/risk-service"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"
)

const (
	digicardCore = "digicard_core"
)

var (
	wfInit    = we.InitExecution
	wfGet     = we.GetExecution
	wfExecute = we.Execute
)

// WorkflowImpl is the implementation of the workflow
type WorkflowImpl struct {
	HedwigClient             hedwig.Notifier                      `inject:"client.hedwig"` // seems like we can remove HedwigClient
	AccountServiceClient     accountAPIV2.AccountService          `inject:"client.accountServiceV2"`
	RiskServiceClient        riskAPI.RiskService                  `inject:"client.riskService"`
	CustomerMasterDBMYClient customerMasterDBMYAPI.CustomerMaster `inject:"client.customerMasterDBMY"`
	StatsD                   statsd.Client                        `inject:"statsD"`
	EuronetAdapterClient     euronetAdapterAPI.EuronetAdapter     `inject:"client.euronetAdapter"`
	FeatureFlags             *config.FeatureFlags                 `inject:"config.featureFlags"`
	KafkaWriter              kafkawriter.Client                   `inject:"writer.daStream"`
	WorkflowRetryConfig      *config.WorkflowRetryConfig          `inject:"config.workflowRetry"`
	CardIssuanceConfig       config.CardIssuanceConfig            `inject:"config.cardIssuanceConfig"`
	CountryConfig            config.CountryConfig                 `inject:"config.countryConfig"`
	PushNotifier             notification.Notifier                `inject:"notifier.pushNotifier"`
	EmailNotifier            notification.Notifier                `inject:"notifier.emailNotifier"`
}

// ExecutionData is the state machine internal representation
type ExecutionData struct {
	Card                    storage.Card
	State                   we.State
	IdempotencyKey          string
	CustomerID              string
	Request                 *api.CreateCardRequest
	EuronetAdapterResp      *euronetAdapterAPI.CardIssuanceResponse
	Status                  string
	StatusReason            string
	StatusReasonDescription string
	CardDesign              storage.CardDesign
	ProductVariant          string                    // Added: Product variant
	ProductConfig           *constant.ProductConfig  // Added: Product configuration
}

const (
	workflowID = "createCardWorkflow"
)

// states definition:
const (
	stInit               = we.StateInit
	stCardPersisted      = we.State(100)
	stVirtualIssued      = we.State(201)
	stVirtualRiskChecked = we.State(210)
	stPushNotified       = we.State(215)
	stEmailNotified      = we.State(220)

	stFailed = we.State(500)
)

const (
	evNoNeed                        = we.EventNoNeed
	evPersistAndProcessCardCreation = we.Event(101)
	evRetryMaxAttemptReached        = we.Event(200)
)

// RegisterWorkflow should be called during initialisation to register individual workflow to the workflow engine
func (w *WorkflowImpl) RegisterWorkflow() {
	createCardWorkflow := we.NewWorkflow(workflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})
	var virtualCardCreationRetry *we.TransitionOptions
	var virtualCardNotificationRetry *we.TransitionOptions
	if retryOption := w.WorkflowRetryConfig.VirtualCardCreation; retryOption != nil {
		virtualCardCreationRetry = &we.TransitionOptions{RetryPolicy: &we.RetryPolicy{
			Interval:    retryOption.IntervalInSeconds,
			MaxAttempts: retryOption.MaxAttempt,
			FinalEvent:  evRetryMaxAttemptReached}}
	}
	if retryOption := w.WorkflowRetryConfig.VirtualCardNotificationRetry; retryOption != nil {
		virtualCardNotificationRetry = &we.TransitionOptions{RetryPolicy: &we.RetryPolicy{
			Interval:    retryOption.IntervalInSeconds,
			MaxAttempts: retryOption.MaxAttempt,
			FinalEvent:  evRetryMaxAttemptReached}}
	}
	createCardWorkflow.AddTransition(logTag, stInit, evPersistAndProcessCardCreation, w.persistCard, nil, stCardPersisted, stFailed)
	createCardWorkflow.AddTransition(logTag, stCardPersisted, evNoNeed, w.riskCheck, nil, stVirtualRiskChecked, stFailed)
	createCardWorkflow.AddTransition(logTag, stVirtualRiskChecked, evNoNeed, w.issueVirtualCard, virtualCardCreationRetry, stVirtualIssued, stFailed)
	createCardWorkflow.AddTransition(logTag, stVirtualRiskChecked, evRetryMaxAttemptReached, w.updateCardCreationToFailed, nil, stFailed)
	createCardWorkflow.AddTransition(logTag, stVirtualIssued, evNoNeed, w.sendPush, virtualCardNotificationRetry, stPushNotified)
	createCardWorkflow.AddTransition(logTag, stPushNotified, evNoNeed, w.sendEmail, virtualCardNotificationRetry, stEmailNotified)
	we.RegisterWorkflow(createCardWorkflow)
}

// GetWorkflow get the current execution of the workflow
func (w *WorkflowImpl) GetWorkflow(ctx context.Context, runID string) (*dto.WorkflowResponse, error) {
	execData, err := we.GetExecution(ctx, we.Execution{
		WorkflowID: workflowID,
		RequestID:  runID,
	})
	if err != nil {
		return nil, api.DefaultInternalServerError
	}

	return &dto.WorkflowResponse{
		State:                  execData.GetState(),
		Status:                 StateToStatus(execData.GetState()),
		InternalIdempotencyKey: execData.(*ExecutionData).Card.UserID,
		Payload:                execData.(*ExecutionData),
	}, nil
}

// StateToStatus is temporary function to convert internal system state to human readable status
func StateToStatus(state we.State) string {
	// TODO: fix the mapping
	state /= 100.0
	switch state {
	case 0, 1, 2:
		return "PROCESSING"
	case 5:
		return "FAILED"
	case 9:
		return "COMPLETED"
	}
	return "PROCESSING"
}

// Stats ...
func (w *WorkflowImpl) Stats(ctx context.Context, status string, statusReason string) {
	slog.FromContext(ctx).Debug(logTag, fmt.Sprintf("publishing %s metric", status))
	w.StatsD.Count1(digicardCore, logTag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}
