package createcard

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	riskClient "gitlab.myteksi.net/dakota/client-sdk/risk-service"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	sm "gitlab.myteksi.net/dakota/state-machine"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	physicalCardReasonCode = "NEW_PHYSICAL_CARD"
	virtualCardReasonCode  = "NEW_VIRTUAL CARD"
)

func (w *WorkflowImpl) riskCheck(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "Invalid context passed in checkLimit state")
		return nil, logic.ErrInvalidContext
	}

	nextCtx := currCtx.Clone()

	productVariant := constant.ProductVariant(nextCtx.Card.ProductVariant)
	if bypassCtx := w.bypassCheck(ctx, nextCtx, productVariant); bypassCtx != nil {
		return bypassCtx, nil
	}

	riskReq := convertCheckpointRequest(ctx, &nextCtx.Card, nextCtx.GetState())

	riskResp, err := w.RiskServiceClient.ValidatePartnerTxn(ctx, riskReq)
	if err != nil {
		servusError, ok := err.(servus.ServiceError)
		if ok && servusError.HTTPCode/100 == 5 {
			slog.FromContext(ctx).Warn(logTag, "error getting response from riskCheck, 5xx bypassing risk check", slog.Error(err))
			nextCtx.State = stVirtualRiskChecked
			return nextCtx, nil
		}
		slog.FromContext(ctx).Warn(logTag, "error getting response from riskCheck", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("risk check response - %+v", riskResp))
	switch riskResp.StatusCode {
	case riskClient.Allow:
		nextCtx.State = stVirtualRiskChecked
		return nextCtx, nil
	case riskClient.Deny:
		nextCtx.Card.Status = string(constant.CardStatusFailed)
		nextCtx.State = stFailed
		nextCtx.StatusReason = "VIRTUAL_RISK_CHECK_FAILED"
		nextCtx.StatusReasonDescription = "virtual card risk check failed"
		if currCtx.GetState() == stVirtualIssued {
			nextCtx.StatusReason = "PHYSICAL_RISK_CHECK_FAILED"
			nextCtx.StatusReasonDescription = "physical card risk check failed"
		}
		nextCtx.Status = constant.Failed
		w.Stats(ctx, constant.Failed, nextCtx.StatusReason)
		return nextCtx, nil
	default:
		slog.FromContext(ctx).Warn(logTag, "risk Check Failed - undefined risk check result")
		return nil, api.DefaultInternalServerError
	}
}

func (w *WorkflowImpl) bypassCheck(ctx context.Context, nextCtx workflowengine.ExecutionData, productVariant constant.ProductVariant) workflowengine.ExecutionData {
	if nextCtx.GetState() == stCardPersisted && !w.FeatureFlags.EnableVirtualCardCreationRiskCheck {
		slog.FromContext(ctx).Info(logTag, "virtual card creation risk check not enabled, bypassing")
		nextCtx.SetState(stVirtualRiskChecked)
		return nextCtx
	} else if nextCtx.GetState() == stCardPersisted && productVariant == constant.MYCreditCard { // TODO: flag to bypass for pilot for now
		slog.FromContext(ctx).Info(logTag, "cc virtual card creation risk check not enabled, bypassing")
		nextCtx.SetState(stVirtualRiskChecked)
		return nextCtx
	}
	return nil
}

func convertCheckpointRequest(ctx context.Context, card *storage.Card, state sm.State) *riskClient.ValidatePartnerTxnRequest {
	reasonCode := virtualCardReasonCode
	if state == stVirtualIssued {
		reasonCode = physicalCardReasonCode
	}
	payload := &riskClient.CardActivityPayload{
		CustomerID: card.UserID,
		DeviceID:   commonCtx.GetDeviceID(ctx),
		IPAddress:  commonCtx.GetTrueClientIP(ctx),
		CardID:     card.CardID,
		ReasonCode: reasonCode,
		CreatedAt:  card.CreatedAt,
	}

	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("risk checkpoint payload %+v", payload))
	payloadByte, _ := json.Marshal(payload)
	return &riskClient.ValidatePartnerTxnRequest{
		TransactionType: riskClient.CardActivity,
		Partner:         riskClient.DIGIBANK,
		Payload:         (*json.RawMessage)(&payloadByte),
	}
}
