// Code generated by mockery v2.53.3. DO NOT EDIT.

package createcard

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockAccountVerifyClient is an autogenerated mock type for the AccountVerifyClient type
type MockAccountVerifyClient struct {
	mock.Mock
}

// VerifyAccount provides a mock function with given fields: ctx, accountID, productVariant
func (_m *MockAccountVerifyClient) VerifyAccount(ctx context.Context, accountID string, productVariant string) error {
	ret := _m.Called(ctx, accountID, productVariant)

	if len(ret) == 0 {
		panic("no return value specified for VerifyAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, accountID, productVariant)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockAccountVerifyClient creates a new instance of MockAccountVerifyClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAccountVerifyClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAccountVerifyClient {
	mock := &MockAccountVerifyClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
