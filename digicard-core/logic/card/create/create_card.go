// Package createcard provides the logic for executing create card workflow
package createcard

import (
	"context"
	"errors"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	card_design_utils "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// ExecuteCreateCardWorkflow ...
func (w *WorkflowImpl) ExecuteCreateCardWorkflow(ctx context.Context, req *api.CreateCardRequest, productConfig *constant.ProductConfig) (*api.CardResponse, error) {
	userID := commonCtx.GetUserID(ctx)

	// Use product configuration passed from handler
	var productVariant string
	if productConfig != nil {
		productVariant = string(productConfig.ProductVariant)
	} else {
		productVariant = string(constant.MYDebitCard)
	}

	data := &ExecutionData{
		State:          stInit,
		Request:        req,
		CustomerID:     userID,
		IdempotencyKey: req.IdempotencyKey,
		ProductVariant: productVariant, // Store product variant from handler
		ProductConfig:  productConfig,  // Store product config from handler
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constant.IdempotencyKeyTag, data.IdempotencyKey),
		slog.CustomTag("customerID", data.CustomerID),
		slog.CustomTag("accountID", data.Request.AccountID))

	cardDesign, err := logic.FetchCardDesignByCardDesignID(ctx, req.CardDesignId)
	if err != nil {
		return nil, err
	}
	data.CardDesign = *cardDesign

	cardCreation, err := executeVirtualCardCreation(ctx, data, w.CardIssuanceConfig)

	return cardCreation, err
}

func executeVirtualCardCreation(ctx context.Context, data *ExecutionData, config config.CardIssuanceConfig) (*api.CardResponse, error) {
	// Use product-specific card limit checking
	if err := checkCardsLimitWithProduct(ctx, data.CustomerID, data.ProductVariant); err != nil {
		return nil, err
	}

	//availableCardDesigns, err := logic.FetchAvailableCardDesigns(ctx)
	availableCardDesigns, err := logic.FetchAvailableCardDesignsByProductVariant(ctx, data.ProductVariant)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error in finding available card Design", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	if len(availableCardDesigns) > 0 {
		if data.CardDesign.StockCount < card_design_utils.CardDesignBenchmark {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Selected card design is not available for the card %s", data.Request.CardID))
			return nil, servus.ServiceError{
				Code:     string(api.ResourceConflict),
				Message:  "Selected card design is not available.",
				HTTPCode: api.ResourceConflict.HTTPStatusCode(),
			}
		}
	}

	if err = wfInit(ctx, workflowengine.Execution{
		WorkflowID: workflowID,
		RequestID:  data.IdempotencyKey,
	}, data); err != nil {
		if !errors.Is(err, workflowengine.ErrExecutionAlreadyExists) {
			slog.FromContext(ctx).Warn(logTag, "error when initiating workflow", slog.Error(err))
			return nil, err
		}

		//attempt to check if existing workflow already exists
		//nolint
		execData, getErr := wfGet(ctx, workflowengine.Execution{
			WorkflowID: workflowID,
			RequestID:  data.IdempotencyKey,
		})
		if getErr != nil {
			slog.FromContext(ctx).Warn(logTag, "error when getting existing workflow", slog.Error(getErr))
			return nil, getErr
		}
		wfData := execData.(*ExecutionData)
		wfData.StatusReason = err.Error()

		return convertResponse(wfData)
	}

	execData, err := wfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      data.Request.IdempotencyKey,
		ExecutionEvent: evPersistAndProcessCardCreation,
	}, nil)
	if err != nil {
		if errors.Is(err, constant.ErrRetryable) {
			slog.FromContext(ctx).Info(logTag, "error from running workflow, returning processing for retry to kick in", slog.Error(err))
			return &api.CardResponse{
				Status:       constant.Processing,
				StatusReason: "error calling euronet issuance API",
			}, nil
		}
		slog.FromContext(ctx).Warn(logTag, "error when running workflow", slog.Error(err))
		return nil, logic.ErrGenericServer
	}
	return convertResponse(execData.(*ExecutionData))
}

func convertResponse(data *ExecutionData) (*api.CardResponse, error) {
	var card *api.CardDetail
	if data.Status == constant.Success {
		card = &api.CardDetail{
			CardID:         data.Card.CardID,
			TailCardNumber: data.Card.TailCardNumber,
			HeadCardNumber: data.Card.HeadCardNumber,
			DisplayName:    data.Card.DisplayName,
			Status:         data.Card.Status,
			VendorCardID:   data.Card.VendorCardID,
			OrderStatus:    data.Card.OrderStatus,
			AccountID:      data.Card.AccountID,
			CardDesign:     card_design_utils.ToCardDesignDetail(data.CardDesign, true), // The Design Availability is needed in virtual card creation response as it is used to determine to proceed/not proceed with Physical Card Flow
			CardType:       logic.GetCardType(&data.Card),
		}
	}
	return &api.CardResponse{
		Card:                    card,
		Status:                  data.Status,
		StatusReason:            data.StatusReason,
		StatusReasonDescription: "",
	}, nil
}

func checkCardsLimit(ctx context.Context, userID string, config config.CardIssuanceConfig) error {
	cards, err := logic.FetchAllCards(ctx, userID)
	if err != nil {
		return err
	}

	successfulCardsCount := 0
	activeCardsCount := 0

	for _, card := range cards {
		if card.Status == string(constant.CardStatusProcessing) || card.Status == string(constant.CardStatusLocked) {
			return logic.ErrExceededMaxAllowedActiveCards
		}

		if card.Status == string(constant.CardStatusActive) {
			activeCardsCount++
			if activeCardsCount >= config.MaxAllowedActiveCards {
				return logic.ErrExceededMaxAllowedActiveCards
			}
		}

		if card.Status != string(constant.Failed) {
			successfulCardsCount++
			if successfulCardsCount > config.MaxAllowedCards {
				return logic.ErrExceededMaxAllowedCards
			}
		}
	}

	return nil
}

// Enhanced function with product variant support
func checkCardsLimitWithProduct(ctx context.Context, userID string, productVariant string) error {
	// Fetch cards filtered by product variant for accurate counting
	cards, err := logic.FetchCardsByProductVariant(ctx, userID, productVariant)
	if err != nil {
		return err
	}

	successfulCardsCount := 0
	activeCardsCount := 0

	for _, card := range cards {
		if card.Status == string(constant.CardStatusProcessing) || card.Status == string(constant.CardStatusLocked) {
			return logic.ErrExceededMaxAllowedActiveCards
		}

		if card.Status == string(constant.CardStatusActive) {
			activeCardsCount++
			if activeCardsCount >= config.MaxAllowedActiveCards {
				return logic.ErrExceededMaxAllowedActiveCards
			}
		}

		if card.Status != string(constant.Failed) {
			successfulCardsCount++
			if successfulCardsCount > config.MaxAllowedCards {
				return logic.ErrExceededMaxAllowedCards
			}
		}
	}

	return nil
}
