package createcard

import (
	"context"
	"net/http"
	"strings"
	"time"

	"gitlab.myteksi.net/gophers/go/commons/util/tags"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	logTag           = "CreateCard"
	errServusTimeout = "We're sorry but the request timed out"
)

func (w *WorkflowImpl) issueVirtualCard(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "error in getting current context")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, commonCtx.GetUserID(ctx))
	resp, err := w.EuronetAdapterClient.CardIssuance(ctx, &euronetAdapterAPI.CardIssuanceRequest{ // TODO: Check if need to pass product variant
		CardID:           nextCtx.Card.CardID,
		CardIssuanceType: string(constant.VirtualCard),
		IdempotencyKey:   currCtx.IdempotencyKey,
		DisplayName:      nextCtx.Request.DisplayName,
		// TODO: to pass VendorIdentifier?
	})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error calling euronet adapter card issuance API", slog.Error(err))
		if enaErr, ok := err.(*errorhandling.Error); ok { // timeout error, we don't know if the issuance is successful in en-a or en
			slog.FromContext(ctx).Info(logTag, "err type assertion successs",
				tags.T("code", enaErr.Code),
				tags.T("httpCode", enaErr.HTTPCode),
				tags.T("mesg", enaErr.Message))
			if (enaErr.HTTPCode == http.StatusServiceUnavailable && enaErr.Message == errServusTimeout) ||
				(enaErr.HTTPCode == http.StatusInternalServerError && strings.Contains(enaErr.Message, constant.ErrRetryable.Error())) {
				slog.FromContext(ctx).Warn(logTag, "timeout happened", tags.T("code", enaErr.Code))
				return nil, constant.ErrRetryable
			}
		}
		nextCtx.State = stFailed
		nextCtx.StatusReason = err.Error()
		nextCtx.Status = constant.Failed
		w.Stats(ctx, constant.Failed, err.Error())
		return nextCtx, nil
	}
	switch resp.Data.Status {
	case constant.Success:
		setVirtualCardInfo(resp, nextCtx)
		w.Stats(ctx, logic.CreatedTag, "")
	case constant.Failed:
		nextCtx.Card.Status = string(constant.CardStatusFailed)
		nextCtx.State = stFailed
		nextCtx.StatusReason, nextCtx.StatusReasonDescription = resp.Data.StatusReason, resp.Data.StatusMessage // TODO: how to better handle status reason
		nextCtx.Status = constant.Failed
		w.Stats(ctx, constant.Failed, nextCtx.StatusReason)
	default:
		slog.FromContext(ctx).Warn(logTag, "invalid card issuance response status") //TODO: handle this more elegantly
		return nil, logic.ErrInvalidResponseStatus
	}
	nextCtx.Card.UpdatedAt = time.Now()
	err = storage.CardDao.UpdateEntity(ctx, &currCtx.Card, &nextCtx.Card)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error updating status in db")
		return nil, err
	}
	// Publishing event only on successful card issuance
	// TODO: Need to handle an edge case when save is successful but publishing fails
	if resp.Data.Status == constant.Success {
		bgCtx := bgcontext.BackgroundWithValue(ctx)
		gconcurrent.Go(bgCtx, logTag, func(ctx context.Context) error {
			if err = w.publishActivity(bgCtx, &nextCtx.Card); err != nil {
				slog.FromContext(bgCtx).Warn(logTag, "publishCardActivity: publishing card activity to kafka failed", slog.Error(err))
				return err
			}
			return nil
		})
	}
	return nextCtx, nil
}

func (w *WorkflowImpl) publishActivity(ctx context.Context, card *storage.Card) error {
	deviceID := commonCtx.GetDeviceID(ctx)
	ipAddress := commonCtx.GetTrueClientIP(ctx)

	cardDTO := logic.ActivityDTOFromCard(card, deviceID, ipAddress, logic.VirtualCardCreate)
	if err := w.KafkaWriter.Save(logic.ConstructTxObj(cardDTO)); err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to publish async response", slog.Error(err))
		return err
	}
	return nil
}

func setVirtualCardInfo(resp *euronetAdapterAPI.CardIssuanceResponse, nextCtx *ExecutionData) {
	if len(resp.Data.MaskedCardNumber) != 0 {
		nextCtx.Card.TailCardNumber = resp.Data.MaskedCardNumber[len(resp.Data.MaskedCardNumber)-4:]
		nextCtx.Card.HeadCardNumber = resp.Data.MaskedCardNumber[0:6]
	}

	nextCtx.Card.ExpiryDate = resp.Data.ExpiryDate
	nextCtx.Card.CardSequenceNo = int(resp.Data.CardSequenceNumber)
	nextCtx.Card.VendorCardID = resp.Data.ProxyNumber
	nextCtx.Card.Status = string(constant.CardStatusActive)
	nextCtx.State = stVirtualIssued
	nextCtx.Status = constant.Success
	nextCtx.Card.ActivatedAt = time.Now()
}
