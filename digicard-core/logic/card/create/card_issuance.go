package createcard

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"gitlab.myteksi.net/gophers/go/commons/util/tags"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	logTag           = "CreateCard"
	errServusTimeout = "We're sorry but the request timed out"
)

func (w *WorkflowImpl) issueVirtualCard(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "error in getting current context")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, commonCtx.GetUserID(ctx))
	// Get product configuration for vendor identifier
	productVariant := constant.ProductVariant(nextCtx.Card.ProductVariant)
	//productConfig := constant.GetProductConfig(productVariant)

	// Build request with product-specific parameters
	cardIssuanceReq := &euronetAdapterAPI.CardIssuanceRequest{
		CardID:           nextCtx.Card.CardID,
		CardIssuanceType: string(constant.VirtualCard),
		IdempotencyKey:   currCtx.IdempotencyKey,
		DisplayName:      nextCtx.Request.DisplayName,
		ProductVariant:   string(productVariant),
	}

	// TODO: to pass VendorIdentifier?
	// Set vendor identifier from product configuration
	//if productConfig != nil {
	//	cardIssuanceReq.VendorIdentifier = productConfig.VendorIdentifier // "CCM" for CCC, "MDP" for debit
	//}

	resp, err := w.EuronetAdapterClient.CardIssuance(ctx, cardIssuanceReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error calling euronet adapter card issuance API", slog.Error(err))
		if enaErr, ok := err.(*errorhandling.Error); ok { // timeout error, we don't know if the issuance is successful in en-a or en
			slog.FromContext(ctx).Info(logTag, "err type assertion successs",
				tags.T("code", enaErr.Code),
				tags.T("httpCode", enaErr.HTTPCode),
				tags.T("mesg", enaErr.Message))
			if (enaErr.HTTPCode == http.StatusServiceUnavailable && enaErr.Message == errServusTimeout) ||
				(enaErr.HTTPCode == http.StatusInternalServerError && strings.Contains(enaErr.Message, constant.ErrRetryable.Error())) {
				slog.FromContext(ctx).Warn(logTag, "timeout happened", tags.T("code", enaErr.Code))
				return nil, constant.ErrRetryable
			}
		}
		nextCtx.State = stFailed
		nextCtx.StatusReason = err.Error()
		nextCtx.Status = constant.Failed
		w.Stats(ctx, constant.Failed, err.Error())
		return nextCtx, nil
	}
	switch resp.Data.Status {
	case constant.Success:
		setVirtualCardInfo(resp, nextCtx)
		w.Stats(ctx, logic.CreatedTag, "")
	case constant.Failed:
		nextCtx.Card.Status = string(constant.CardStatusFailed)
		nextCtx.State = stFailed
		nextCtx.StatusReason, nextCtx.StatusReasonDescription = resp.Data.StatusReason, resp.Data.StatusMessage // TODO: how to better handle status reason
		nextCtx.Status = constant.Failed
		w.Stats(ctx, constant.Failed, nextCtx.StatusReason)
	default:
		slog.FromContext(ctx).Warn(logTag, "invalid card issuance response status") //TODO: handle this more elegantly
		return nil, logic.ErrInvalidResponseStatus
	}
	nextCtx.Card.UpdatedAt = time.Now()
	err = storage.CardDao.UpdateEntity(ctx, &currCtx.Card, &nextCtx.Card)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error updating status in db")
		return nil, err
	}
	// After successful card issuance, set default transaction limits
	if resp.Data.Status == constant.Success {
		// Set product-specific default transaction limits
		productVariant = constant.ProductVariant(nextCtx.Card.ProductVariant)
		if err = w.setDefaultTransactionLimitsByProduct(ctx, &nextCtx.Card, productVariant); err != nil {
			slog.FromContext(ctx).Warn(logTag, "Failed to set default transaction limits", slog.Error(err))
			// Don't fail the entire operation for limit setting failure
		}

		// Publishing event only on successful card issuance
		// TODO: Need to handle an edge case when save is successful but publishing fails
		bgCtx := bgcontext.BackgroundWithValue(ctx)
		gconcurrent.Go(bgCtx, logTag, func(ctx context.Context) error {
			if err = w.publishActivity(bgCtx, &nextCtx.Card); err != nil {
				slog.FromContext(bgCtx).Warn(logTag, "publishCardActivity: publishing card activity to kafka failed", slog.Error(err))
				return err
			}
			return nil
		})
	}
	return nextCtx, nil
}

func (w *WorkflowImpl) publishActivity(ctx context.Context, card *storage.Card) error {
	deviceID := commonCtx.GetDeviceID(ctx)
	ipAddress := commonCtx.GetTrueClientIP(ctx)

	cardDTO := logic.ActivityDTOFromCard(card, deviceID, ipAddress, logic.VirtualCardCreate)
	if err := w.KafkaWriter.Save(logic.ConstructTxObj(cardDTO)); err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to publish async response", slog.Error(err))
		return err
	}
	return nil
}

func (w *WorkflowImpl) setDefaultTransactionLimitsByProduct(ctx context.Context, card *storage.Card, productVariant constant.ProductVariant) error {
	productConfig := constant.GetProductConfig(productVariant)
	if productConfig == nil {
		return fmt.Errorf("no product config found for variant: %s", productVariant)
	}

	limitNames := productConfig.TxnLimitNames

	// Default limits based on product variant
	var defaultLimits map[string]int64
	if productVariant == constant.MYCreditCard {
		// CCC default limits
		defaultLimits = map[string]int64{
			limitNames.Contactless: 50000,  // 500 MYR
			limitNames.Online:      100000, // 1000 MYR
			limitNames.PinAndPay:   50000,  // 500 MYR
			limitNames.ATM:         30000,  // 300 MYR
		}
	} else {
		// Debit card default limits (keep existing)
		defaultLimits = map[string]int64{
			limitNames.Contactless: 25000,  // 250 MYR
			limitNames.Online:      50000,  // 500 MYR
			limitNames.PinAndPay:   25000,  // 250 MYR
			limitNames.ATM:         150000, // 1500 MYR
		}
	}

	// Set each limit via transaction limit service
	for limitName, amount := range defaultLimits {
		if err := w.setTransactionLimit(ctx, card.CardID, limitName, amount); err != nil {
			return fmt.Errorf("failed to set %s limit: %w", limitName, err)
		}
	}

	return nil
}

func (w *WorkflowImpl) setTransactionLimit(ctx context.Context, cardID, limitName string, amount int64) error {
	// Implementation for setting transaction limits
	// This would call the transaction limit service
	slog.FromContext(ctx).Info("setting_transaction_limit",
		slog.CustomTag("cardID", cardID),
		slog.CustomTag("limitName", limitName),
		slog.CustomTag("amount", amount))

	// Placeholder - actual implementation would call transaction limit service
	return nil
}

func setVirtualCardInfo(resp *euronetAdapterAPI.CardIssuanceResponse, nextCtx *ExecutionData) {
	if len(resp.Data.MaskedCardNumber) != 0 {
		nextCtx.Card.TailCardNumber = resp.Data.MaskedCardNumber[len(resp.Data.MaskedCardNumber)-4:]
		nextCtx.Card.HeadCardNumber = resp.Data.MaskedCardNumber[0:6]
	}

	nextCtx.Card.ExpiryDate = resp.Data.ExpiryDate
	nextCtx.Card.CardSequenceNo = int(resp.Data.CardSequenceNumber)
	nextCtx.Card.VendorCardID = resp.Data.ProxyNumber
	nextCtx.Card.Status = string(constant.CardStatusActive)
	nextCtx.State = stVirtualIssued
	nextCtx.Status = constant.Success
	nextCtx.Card.ActivatedAt = time.Now()
}
