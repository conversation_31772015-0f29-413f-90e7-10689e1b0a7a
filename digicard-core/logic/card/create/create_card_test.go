package createcard

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/external/hedwig"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/utils/cache"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	userID         = "USER_ID"
	name           = "ABC"
	accountIDDummy = "**********"
	idempotencyKey = "{{$guid}}"
)

var testCountryConfig = config.CountryConfig{
	CurrencyCode: "MYR",
	CurrencyNum:  "458",
	CountryCode:  "MY",
}

var testCardIssuanceConfig = config.CardIssuanceConfig{
	MaxAllowedCards:       1,
	MaxAllowedActiveCards: 1,
}

func TestConvertResponse(t *testing.T) {
	mockRedis := &mocks.Client{}
	cache.RedisClient = mockRedis
	t.Run("active card status", func(t *testing.T) {
		data := ExecutionData{
			Status: constant.Success,
			Card: storage.Card{
				ID:           0,
				PostScript:   nil,
				ActivatedAt:  time.Now(),
				CreatedAt:    time.Time{},
				UpdatedAt:    time.Time{},
				CardDesignID: "a87d3ed6-2dda-11ed-95fa-cf6eb74e5eee",
			},
		}

		mockCardDesignDAO := &storage.MockICardDesignDAO{}
		storage.CardDesignDao = mockCardDesignDAO

		mockRedis.On("Set").Return(true, nil)
		mockRedis.On("GetBytes").Return(nil, redis.ErrNoData)
		mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)
		mockRedis.On("GetBytes", mock.Anything, mock.Anything).Return(nil, redis.ErrNoData)

		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{
			{
				CardDesignID: "a87d3ed6-2dda-11ed-95fa-cf6eb74e5eee",
				Color:        "#FFFFFF",
				Description:  "White card",
				FrontImage:   "white_front.png",
				BackImage:    "white_back.png",
			},
		}, nil)
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardDesign{
			{
				CardDesignID: "a87d3ed6-2dda-11ed-95fa-cf6eb74e5eee",
				Color:        "#FFFFFF",
				Description:  "White card",
				FrontImage:   "white_front.png",
				BackImage:    "white_back.png",
			},
		}, nil)

		_, err := convertResponse(&data)
		assert.Equal(t, err, nil)
	})
	t.Run("internal failure", func(t *testing.T) {
		data := ExecutionData{Status: constant.Failed}
		resp, err := convertResponse(&data)
		assert.Nil(t, err)
		assert.Equal(t, constant.Failed, resp.Status)
	})
}

func TestExecuteCreateCardWorkflow(t *testing.T) {
	setMockDataSet()

	//defer testtools.Restore(testtools.Pairs(&logic.GetHeaderCtx, &logic.GetUserID, &logic.GetServiceID, &wfInit, &wfGet, &wfExecute)...)
	errDummy := errors.New("simulate error")
	testUserID := uuid.NewString()
	testIdempotencyKey := uuid.NewString()
	testType := string(constant.VirtualCard)
	testAccountID := accountIDDummy
	testDisplayName := name
	testCardDesignID := test.TestCardDesignID
	defaultCreateCardRequest := &api.CreateCardRequest{
		Type:           testType,
		AccountID:      testAccountID,
		DisplayName:    testDisplayName,
		IdempotencyKey: testIdempotencyKey,
		CardDesignId:   testCardDesignID,
	}
	scenarios := []struct {
		userID          string
		createCardReq   *api.CreateCardRequest
		mockCardStatus  constant.CardStatus
		mockOrderStatus constant.OrderStatus
		mockReasonCode  string
		errName         error
		errType         error
		errAddress      error
		errAccountID    error
		errIdempotency  error
		errInit         error
		errGet          error
		errExecute      error
		expectedErr     error
	}{
		{
			userID:        testUserID,
			createCardReq: defaultCreateCardRequest,
			errInit:       errDummy,
			expectedErr:   errDummy,
		},
		{
			userID:        testUserID,
			createCardReq: defaultCreateCardRequest,
			errInit:       workflowengine.ErrExecutionAlreadyExists,
			errGet:        errDummy,
			expectedErr:   errDummy,
		},
		{
			userID:        testUserID,
			createCardReq: defaultCreateCardRequest,
			errInit:       workflowengine.ErrExecutionAlreadyExists,
		},
		{
			userID:        testUserID,
			createCardReq: defaultCreateCardRequest,
			errExecute:    errDummy,
			expectedErr:   logic.ErrGenericServer,
		},
		{
			userID:        testUserID,
			createCardReq: defaultCreateCardRequest,
			errExecute:    constant.ErrRetryable,
		},
		{
			userID:        testUserID,
			createCardReq: defaultCreateCardRequest,
			errGet:        errDummy,
		},
		{
			userID:         testUserID,
			createCardReq:  defaultCreateCardRequest,
			errGet:         errDummy,
			expectedErr:    logic.ErrExceededMaxAllowedActiveCards,
			mockCardStatus: constant.CardStatusProcessing,
		},
		{
			userID:         testUserID,
			createCardReq:  defaultCreateCardRequest,
			errGet:         errDummy,
			expectedErr:    logic.ErrExceededMaxAllowedActiveCards,
			mockCardStatus: constant.CardStatusLocked,
		},
		{
			userID:         testUserID,
			createCardReq:  defaultCreateCardRequest,
			errGet:         errDummy,
			expectedErr:    logic.ErrExceededMaxAllowedActiveCards,
			mockCardStatus: constant.CardStatusActive,
		},
	}
	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		logic.GetHeaderCtx = func(ctx context.Context) http.Header {
			headers := http.Header{}
			headers.Set(logic.GetUserID(context.Background()), userID)
			return headers
		}
		logic.GetUserID = func(ctx context.Context) string {
			return testcase.userID
		}
		wfInit = func(ctx context.Context, e workflowengine.Execution, executionData workflowengine.ExecutionData) error {
			return testcase.errInit
		}
		wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				IdempotencyKey: uuid.NewString(),
				Request:        defaultCreateCardRequest,
				Status:         constant.Success,
				Card:           storage.Card{Status: string(constant.CardStatusActive), CardDesignID: testCardDesignID},
			}, testcase.errGet
		}
		wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				IdempotencyKey: uuid.NewString(),
				Request:        defaultCreateCardRequest,
				Status:         constant.Success,
				Card:           storage.Card{Status: string(constant.CardStatusActive), CardDesignID: testCardDesignID},
			}, testcase.errExecute
		}
		mockHedwigClient := &hedwig.MockNotifier{}
		workflow := WorkflowImpl{
			StatsD:       statsd.NewNoop(),
			HedwigClient: mockHedwigClient,
			FeatureFlags: &config.FeatureFlags{
				EnableAccountStatusEnquiryCheck:                   true,
				EnableCardCreationPermissionAndAccountStatusCheck: true,
			},
			CardIssuanceConfig: testCardIssuanceConfig,
		}
		mockHedwigClient.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		// Mock for enhanced card limits check with product variant support
		// Mock for enhanced card limits check (3 arguments: context + 2 conditions)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
			Status:         string(testcase.mockCardStatus),
			OrderStatus:    string(testcase.mockOrderStatus),
			UserID:         testUserID,
			ReasonCode:     testcase.mockReasonCode,
			ProductVariant: string(constant.MYDebitCard), // Added: Product variant for enhanced logic
		}}, nil).Maybe() // Use Maybe() since not all test cases will trigger card limits check

		ctx := commonCtx.WithUserID(context.Background(), scenario.userID)

		// Use default debit card product config for backward compatibility
		productConfig := constant.GetProductConfig(constant.MYDebitCard)
		resp, err := workflow.ExecuteCreateCardWorkflow(ctx, testcase.createCardReq, productConfig)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, scenario.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
		}
	}
}

func TestExecuteVirtualCardCreation(t *testing.T) {
	testIdempotencyKey := uuid.NewString()
	testType := string(constant.VirtualCard)
	testAccountID := accountIDDummy
	testDisplayName := name
	testCardDesignID := test.TestCardDesignID
	defaultCreateCardRequest := &api.CreateCardRequest{
		Type:           testType,
		AccountID:      testAccountID,
		DisplayName:    testDisplayName,
		IdempotencyKey: testIdempotencyKey,
		CardDesignId:   testCardDesignID,
	}

	t.Run("valid flow", func(t *testing.T) {
		setMockDataSet()
		data := &ExecutionData{
			Request:    defaultCreateCardRequest,
			CardDesign: storage.CardDesign{CardDesignID: testCardDesignID, StockCount: 11},
		}

		wfInit = func(ctx context.Context, e workflowengine.Execution, executionData workflowengine.ExecutionData) error {
			return nil
		}
		wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				IdempotencyKey: uuid.NewString(),
				Request:        defaultCreateCardRequest,
				Status:         constant.Success,
				Card:           storage.Card{Status: string(constant.CardStatusActive), CardDesignID: testCardDesignID},
			}, nil
		}
		wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				IdempotencyKey: uuid.NewString(),
				Request:        defaultCreateCardRequest,
				Status:         constant.Success,
				Card:           storage.Card{Status: string(constant.CardStatusActive), CardDesignID: testCardDesignID},
			}, nil
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{Status: "FAILED", OrderStatus: ""}}, nil).Once()

		resp, err := executeVirtualCardCreation(context.Background(), data, testCardIssuanceConfig)

		assert.Equal(t, err, nil)
		assert.NotNil(t, resp)
	})
	t.Run("FetchAvailableDesigns Error", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()

		test.SetEmptyMockForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)

		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{Status: "FAILED", OrderStatus: ""}}, nil).Once()

		data := &ExecutionData{
			Request:    defaultCreateCardRequest,
			CardDesign: storage.CardDesign{CardDesignID: testCardDesignID, StockCount: 11},
		}

		resp, err := executeVirtualCardCreation(context.Background(), data, testCardIssuanceConfig)

		assert.Equal(t, err, api.DefaultInternalServerError)
		assert.Nil(t, resp)
	})
	t.Run("When one design is active and selected design is inactive", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()

		test.SetMockDataForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)

		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{Status: "FAILED", OrderStatus: ""}}, nil).Once()

		data := &ExecutionData{
			Request:    defaultCreateCardRequest,
			CardDesign: storage.CardDesign{CardDesignID: testCardDesignID, StockCount: 9},
		}

		errorCode := api.ResourceConflict
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "Selected card design is not available.",
			HTTPCode: errorCode.HTTPStatusCode(),
		}

		resp, err := executeVirtualCardCreation(context.Background(), data, testCardIssuanceConfig)

		assert.Equal(t, expectedErr, err)
		assert.Nil(t, resp)
	})
}

func setMockDataSet() {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)
}

func getSimulateError(message string) error {
	errorCode := api.Forbidden
	return servus.ServiceError{
		Code:     string(errorCode),
		Message:  message,
		HTTPCode: errorCode.HTTPStatusCode(),
	}
}
