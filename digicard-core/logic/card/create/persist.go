package createcard

import (
	"context"
	"encoding/json"
	"time"

	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"

	"github.com/google/uuid"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func (w *WorkflowImpl) persistCard(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "error in getting current context")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	req := currCtx.Request
	storageCard, err := w.PersistInCardTable(ctx, req, w.CountryConfig)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error persist card creation into the db")
		nextCtx.SetState(stFailed)
		nextCtx.Status = constant.Failed
		nextCtx.StatusReason = err.Error()
		w.Stats(ctx, constant.Failed, nextCtx.StatusReason)
		return nextCtx, nil
	}
	nextCtx.Card = *storageCard
	nextCtx.SetState(stCardPersisted)
	return nextCtx, nil
}

// PersistInCardTable ...
func (w *WorkflowImpl) PersistInCardTable(ctx context.Context, req *api.CreateCardRequest, config config.CountryConfig) (*storage.Card, error) {
	userID := commonCtx.GetUserID(ctx)
	//	TODO: fill in vendor side details (vendorID, vendorCardID, etc..)
	postScript := &dto.CardPostScript{
		DeliveryAddress: nil,
		ActivationCode:  "",
	}
	postScriptStorage, err := dto.ConvertPostScriptToJSON(postScript)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Invalid json for postScript", slog.Error(err))
		return nil, err
	}
	userToggleSettings := map[string]bool{
		constant.CnpTXN: req.CnpEnabled,
	}
	toggleInfo := GetToggleInfo(userToggleSettings)
	toggleInfoJSON, err := json.Marshal(toggleInfo)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Invalid json for toggleInfo", slog.Error(err))
		return nil, err
	}

	getAccRequest := &accountAPIV2.GetCASAAccountRequest{
		AccountID: req.AccountID,
	}

	accountInfo, err := w.AccountServiceClient.GetCASAAccount(ctx, getAccRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Error getting account info", slog.Error(err))
		return nil, err
	}

	newInsertionRecord := &storage.Card{
		UserID:         userID,
		CardID:         uuid.NewString(),
		AccountID:      req.AccountID,
		DisplayName:    req.DisplayName,
		Status:         string(constant.CardStatusProcessing),
		Currency:       config.CurrencyCode,
		Country:        config.CountryName,
		Provider:       constant.ProviderMasterCard,
		ReferenceID:    req.IdempotencyKey,
		PostScript:     postScriptStorage,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		CardDesignID:   req.CardDesignId,
		ToggleInfo:     toggleInfoJSON,
		VendorID:       constant.VendorEuronet,
		ProductVariant: accountInfo.Account.ProductVariantID, // TODO: get from Execution data
	}

	if err = storage.CardDao.Save(ctx, newInsertionRecord); err != nil {
		return nil, err
	}
	return newInsertionRecord, nil
}

// GetToggleInfo convert user settings from api values to database toggle_info json column
func GetToggleInfo(userSettings map[string]bool) *map[string]string {
	toggleInfo := make(map[string]string)
	for key := range constant.ValidToggles {
		// set toggle value if user has configured this setting when creating the virtual card
		if val, ok := userSettings[key]; ok {
			toggleInfo[key] = parseBoolToToggleAction(val)
			continue
		}
		toggleInfo[key] = string(constant.ToggleDisabled)
	}
	return &toggleInfo
}

func parseBoolToToggleAction(val bool) string {
	if val {
		return string(constant.ToggleEnabled)
	}
	return string(constant.ToggleDisabled)
}
