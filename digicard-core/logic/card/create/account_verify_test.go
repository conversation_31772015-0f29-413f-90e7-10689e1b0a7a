package createcard

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	accountServiceDBMYAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceDBMYMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"
	customerAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

var (
	accountID = uuid.NewString()
)

func TestVerifyAccount(t *testing.T) {
	productConfig := &constant.ProductConfig{
		ProductVariant:   constant.MYDebitCard,
		VendorIdentifier: "MDP",
		TncAgreementID:   string(constant.CardsTermsAgreementID),
		TxnLimitNames: constant.TxnLimits{
			Contactless: "contactless_limit",
			Online:      "online_limit",
			PinAndPay:   "pinpay_limit",
			ATM:         "atm_limit",
		},
	}

	tests := []struct {
		name                   string
		expectedErr            error
		accountID              string
		getCasaAccountResponse *accountServiceDBMYAPI.GetAccountResponse
		lookupCIFNumberResp    *customerAPI.LookupCIFNumberResponse
		lookupCIFNumberErr     error
		checkPermissionResp    *accountServiceDBMYAPI.CheckPermissionsForAccountResponse
		checkPermissionErr     error
		getCasaAccountError    error
	}{
		{
			name:                "failed to get cif number",
			accountID:           accountID,
			lookupCIFNumberResp: nil,
			lookupCIFNumberErr:  errors.New("simulate error"),
			expectedErr:         getSimulateError("simulate error"),
		},
		{
			name:                "failed to check permission for account",
			accountID:           accountID,
			lookupCIFNumberResp: &customerAPI.LookupCIFNumberResponse{CifNumber: "2345"},
			checkPermissionResp: nil,
			checkPermissionErr:  errors.New("simulate error"),
			lookupCIFNumberErr:  nil,
			expectedErr:         getSimulateError("simulate error"),
		},
		{
			name:                "account not found",
			accountID:           accountID,
			lookupCIFNumberResp: &customerAPI.LookupCIFNumberResponse{CifNumber: "2345"},
			checkPermissionResp: nil,
			checkPermissionErr:  errors.New("1202:Missing cif number"),
			lookupCIFNumberErr:  nil,
			expectedErr:         getSimulateError("the source account is not found"),
		},
		{
			name:                "permission not allowed",
			accountID:           accountID,
			lookupCIFNumberResp: &customerAPI.LookupCIFNumberResponse{CifNumber: "2345"},
			checkPermissionResp: &accountServiceDBMYAPI.CheckPermissionsForAccountResponse{Status: accountServiceDBMYAPI.AccountPermission_FORBIDDEN},
			checkPermissionErr:  nil,
			lookupCIFNumberErr:  nil,
			expectedErr:         getSimulateError("the operation is forbidden"),
		},
		{
			name:                "account is not active",
			accountID:           accountID,
			lookupCIFNumberResp: &customerAPI.LookupCIFNumberResponse{CifNumber: "2345"},
			checkPermissionResp: &accountServiceDBMYAPI.CheckPermissionsForAccountResponse{Status: accountServiceDBMYAPI.AccountPermission_ALLOWED},
			checkPermissionErr:  nil,
			lookupCIFNumberErr:  nil,
			getCasaAccountResponse: &accountServiceDBMYAPI.GetAccountResponse{Account: &accountServiceDBMYAPI.Account{
				Id:     accountID,
				Status: accountServiceDBMYAPI.AccountStatus_CLOSED,
			}},
			expectedErr: logic.ErrInactiveAccount,
		},
		{
			name:                "valid account, no error",
			accountID:           accountID,
			lookupCIFNumberResp: &customerAPI.LookupCIFNumberResponse{CifNumber: "2345"},
			checkPermissionResp: &accountServiceDBMYAPI.CheckPermissionsForAccountResponse{Status: accountServiceDBMYAPI.AccountPermission_ALLOWED},
			checkPermissionErr:  nil,
			lookupCIFNumberErr:  nil,
			getCasaAccountResponse: &accountServiceDBMYAPI.GetAccountResponse{Account: &accountServiceDBMYAPI.Account{
				Id:     accountID,
				Status: accountServiceDBMYAPI.AccountStatus_ACTIVE,
			}},
			getCasaAccountError: nil,
			expectedErr:         nil,
		},
	}

	for _, tt := range tests {
		req := tt.accountID
		customerDetailResp := tt.getCasaAccountResponse
		customerDetailRespErr := tt.getCasaAccountError
		lookupCIFNumberResp := tt.lookupCIFNumberResp
		lookupCIFNumberErr := tt.lookupCIFNumberErr
		checkPermissionResp := tt.checkPermissionResp
		checkPermissionErr := tt.checkPermissionErr
		expectedErr := tt.expectedErr
		t.Run(tt.name, func(t *testing.T) {
			mockAccountServiceDBMY := &accountServiceDBMYMock.AccountService{}
			mockAccountServiceDBMY.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(customerDetailResp, customerDetailRespErr).Once()
			mockAccountServiceDBMY.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(checkPermissionResp, checkPermissionErr).Once()
			mockCustomerMaster := &customerMasterMock.CustomerMaster{}
			mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(lookupCIFNumberResp, lookupCIFNumberErr).Once()
			accountVerifyClient := AccountVerifyClientImpl{
				AccountServiceClientV2: mockAccountServiceDBMY,
				CustomerMasterClient:   mockCustomerMaster,
				FeatureFlags: &config.FeatureFlags{
					EnableCardCreationPermissionAndAccountStatusCheck: true,
				},
			}
			err := accountVerifyClient.VerifyAccount(context.Background(), req, string(productConfig.ProductVariant))
			assert.Equal(t, err, expectedErr)
		})
	}
}
