package createcard

import (
	"context"
	"errors"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/enquiry"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
)

const (
	accountVerifyTag = "VerifyAccount"
)

// AccountVerifyClient defines the client to verify given account.
type AccountVerifyClient interface {
	VerifyAccount(ctx context.Context, accountID string) error
}

//go:generate mockery --name AccountVerifyClient --inpackage --case=underscore

// NewAccountVerifyClient creates a new account verify client.
func NewAccountVerifyClient() AccountVerifyClient {
	return &AccountVerifyClientImpl{}
}

// AccountVerifyClientImpl ...
type AccountVerifyClientImpl struct {
	FeatureFlags           *config.FeatureFlags                 `inject:"config.featureFlags"`
	CustomerMasterClient   customerMasterDBMYAPI.CustomerMaster `inject:"client.customerMasterDBMY"`
	AccountServiceClientV2 accountAPIV2.AccountService          `inject:"client.accountServiceV2"`
}

// VerifyAccount ...
func (c *AccountVerifyClientImpl) VerifyAccount(ctx context.Context, accountID string) error {
	if c.FeatureFlags.EnableCardCreationPermissionAndAccountStatusCheck {
		err := c.checkPermission(ctx, accountID)
		if err != nil {
			errorCode := api.Forbidden
			return servus.ServiceError{
				Code:     string(errorCode),
				Message:  err.Error(),
				HTTPCode: errorCode.HTTPStatusCode(),
			}
		}

		// Get product variant to determine account verification method
		accountReq := &accountAPIV2.GetAccountRequest{AccountID: accountID}
		accountResp, err := c.AccountServiceClientV2.GetAccountDetailsByAccountID(ctx, accountReq)
		if err != nil {
			return fmt.Errorf("failed to get account details: %w", err)
		}

		productVariant := constant.ProductVariant(accountResp.Account.ProductVariantID)

		// Product-specific account verification
		switch productVariant {
		case constant.MYCreditCard:
			// For CCC: Verify account is active (account info already available)
			if accountResp.Account.Status != accountAPIV2.AccountStatus_ACTIVE {
				return fmt.Errorf("credit account is not active")
			}
		default:
			// For debit cards: Standard CASA account check
			err = enquiry.CheckCasaAccount(ctx, accountID, c.AccountServiceClientV2)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (c *AccountVerifyClientImpl) checkPermission(ctx context.Context, accountID string) error {
	lookUpCifResponse, err := c.getCifNumber(ctx) // TODO: customer master to get cif number
	if err != nil {
		slog.FromContext(ctx).Warn(accountVerifyTag, "error in getting cifID", slog.Error(err))
		return err
	}

	err = c.checkAccountPermission(ctx, accountID, lookUpCifResponse.CifNumber)
	if err != nil {
		slog.FromContext(ctx).Warn(accountVerifyTag, "error in Checking account permission", slog.Error(err))
		return err
	}
	return nil
}

func (c *AccountVerifyClientImpl) getCifNumber(ctx context.Context) (*customerMasterDBMYAPI.LookupCIFNumberResponse, error) {
	userID := commonCtx.GetUserID(ctx)
	cifReq := &customerMasterDBMYAPI.LookupCIFNumberRequest{
		ID: userID,
		Target: &customerMasterDBMYAPI.TargetGroup{
			ServiceID: constant.ServiceID,
		},
	}
	slog.FromContext(ctx).Debug(accountVerifyTag, "calling customer master - LookupCIFNumber")
	lookUpCifResponse, err := c.CustomerMasterClient.LookupCIFNumber(ctx, cifReq)
	if err != nil {
		slog.FromContext(ctx).Warn(accountVerifyTag, "error in calling LookupCIFNumber", slog.CustomTag("error", err))
		return nil, err
	}
	return lookUpCifResponse, nil
}

func (c *AccountVerifyClientImpl) checkAccountPermission(ctx context.Context, accountID string, cifNumber string) error {
	slog.FromContext(ctx).Debug(accountVerifyTag, "calling account service - CheckPermissionsForAccount")
	request := &accountAPIV2.CheckPermissionsForAccountRequest{
		AccountID: accountID,
		CifNumber: cifNumber,
	}
	response, err := c.AccountServiceClientV2.CheckPermissionsForAccount(ctx, request)
	if err != nil {
		if err.Error() == "1202:Missing cif number" {
			return errors.New("the source account is not found")
		}
		slog.FromContext(ctx).Warn(accountVerifyTag, "AccountServiceClient.CheckPermissionsForAccount error", slog.Error(err))
		return err
	} else if response.Status != accountAPIV2.AccountPermission_ALLOWED {
		return errors.New("the operation is forbidden")
	}
	return nil
}
