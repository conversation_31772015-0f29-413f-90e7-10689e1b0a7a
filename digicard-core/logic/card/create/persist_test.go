package createcard

import (
	"context"
	"testing"
	"time"

	accountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceDBMYMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func TestPersistCard(t *testing.T) {
	t.Run("valid flow", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		mockAccountService := &accountServiceDBMYMock.AccountService{}
		accountResponse := &accountServiceAPI.GetCASAAccountResponse{
			Account: &accountServiceAPI.CASAAccount{
				ProductVariantID: "1234",
			},
		}
		mockKafkaWriter := &kafkawriter.MockClient{}
		storage.CardDao = mockCardDao
		data := ExecutionData{
			Request: &api.CreateCardRequest{
				CnpEnabled: true,
			},
		}
		w := WorkflowImpl{
			StatsD:               statsd.NewNoop(),
			KafkaWriter:          mockKafkaWriter,
			AccountServiceClient: mockAccountService,
		}
		mockAccountService.On("GetCASAAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()
		mockCardDao.On("Save", mock.Anything, mock.Anything).Return(nil)
		_, err := w.persistCard(context.Background(), "", &data, nil)
		assert.Equal(t, nil, err)
	})
	t.Run("error saving card details", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		mockAccountService := &accountServiceDBMYMock.AccountService{}
		accountResponse := &accountServiceAPI.GetCASAAccountResponse{
			Account: &accountServiceAPI.CASAAccount{
				ProductVariantID: "1234",
			},
		}
		storage.CardDao = mockCardDao
		data := ExecutionData{
			Request: &api.CreateCardRequest{},
			Card: storage.Card{
				ID:             0,
				UserID:         "",
				CardID:         "",
				AccountID:      "",
				DisplayName:    "",
				Status:         "",
				HeadCardNumber: "",
				TailCardNumber: "",
				Currency:       "",
				Country:        "",
				Provider:       "",
				VendorID:       "",
				ProductVariant: "",
				PostScript:     nil,
				ActivatedAt:    time.Now(),
				CreatedAt:      time.Time{},
				UpdatedAt:      time.Time{},
			},
		}
		w := WorkflowImpl{
			StatsD:               statsd.NewNoop(),
			AccountServiceClient: mockAccountService,
		}
		mockAccountService.On("GetCASAAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()
		mockCardDao.On("Save", mock.Anything, mock.Anything).Return(api.DefaultInternalServerError)
		_, err := w.persistCard(context.Background(), "", &data, nil)
		assert.Equal(t, nil, err)
	})
}

func TestPersistInCardTable(t *testing.T) {
	// Mock product config for CCC
	productConfig := &constant.ProductConfig{
		ProductVariant:   constant.MYCreditCard,
		VendorIdentifier: "CCM",
		TncAgreementID:   "termsAndConditions_CreditCard_Issuance",
		TxnLimitNames: constant.TxnLimits{
			Contactless: "contactless_limit",
			Online:      "online_limit",
			PinAndPay:   "pinpay_limit",
			ATM:         "atm_limit",
		},
	}

	t.Run("persisting virtual card details", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		mockAccountService := &accountServiceDBMYMock.AccountService{}
		accountResponse := &accountServiceAPI.GetCASAAccountResponse{
			Account: &accountServiceAPI.CASAAccount{
				ProductVariantID: "1234",
			},
		}
		storage.CardDao = mockCardDao
		w := &WorkflowImpl{
			AccountServiceClient: mockAccountService,
		}
		request := api.CreateCardRequest{}
		request.IdempotencyKey = idempotencyKey
		request.AccountID = accountIDDummy
		request.DisplayName = name
		request.Type = string(constant.VirtualCard)
		request.CnpEnabled = true
		mockAccountService.On("GetCASAAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once() //mockAccountServiceDBMY.On("GetAccount", mock.Anything, mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		mockCardDao.On("Save", mock.Anything, mock.Anything).Return(nil)
		record, err := w.PersistInCardTable(context.Background(), &request, testCountryConfig, string(productConfig.ProductVariant))
		assert.Equal(t, nil, err)
		toggleInfo := record.UnmarshalToggleInfo(context.Background())
		assert.Equal(t, toggleInfo[constant.CnpTXN], string(constant.ToggleEnabled))
		assert.Equal(t, toggleInfo[constant.OverseasPOSTXN], string(constant.ToggleDisabled))
	})

	t.Run("persisting physical card details", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		mockAccountService := &accountServiceDBMYMock.AccountService{}
		accountResponse := &accountServiceAPI.GetCASAAccountResponse{
			Account: &accountServiceAPI.CASAAccount{
				ProductVariantID: "1234",
			},
		}
		storage.CardDao = mockCardDao
		w := &WorkflowImpl{
			AccountServiceClient: mockAccountService,
		}
		request := api.CreateCardRequest{}
		request.IdempotencyKey = idempotencyKey
		request.AccountID = accountIDDummy
		request.DisplayName = name
		request.Type = string(constant.PhysicalCard)
		mockAccountService.On("GetCASAAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()
		mockCardDao.On("Save", mock.Anything, mock.Anything).Return(nil)
		_, err := w.PersistInCardTable(context.Background(), &request, testCountryConfig, string(productConfig.ProductVariant))
		assert.Equal(t, nil, err)
	})
	t.Run("error persisting card details", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		mockAccountService := &accountServiceDBMYMock.AccountService{}
		accountResponse := &accountServiceAPI.GetCASAAccountResponse{
			Account: &accountServiceAPI.CASAAccount{
				ProductVariantID: "1234",
			},
		}
		storage.CardDao = mockCardDao
		w := &WorkflowImpl{
			AccountServiceClient: mockAccountService,
		}
		request := api.CreateCardRequest{}
		request.IdempotencyKey = idempotencyKey
		request.AccountID = accountIDDummy
		request.DisplayName = name
		request.Type = string(constant.VirtualCard)
		mockAccountService.On("GetCASAAccount", mock.Anything, mock.Anything).Return(accountResponse, nil).Once()
		mockCardDao.On("Save", mock.Anything, mock.Anything).Return(api.DefaultInternalServerError)
		_, err := w.PersistInCardTable(context.Background(), &request, testCountryConfig, string(productConfig.ProductVariant))
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}
