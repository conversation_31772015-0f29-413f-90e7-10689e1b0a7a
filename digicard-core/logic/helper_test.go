package logic

import (
	"context"
	"encoding/json"
	"errors"
	"regexp"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	customerMasterAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	mockCustomerMaster "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

const (
	userID = "USER_ID"
	empty  = ""
)

var (
	preferredName = "preferredName"
	username      = "username"
)

func TestFetchCardDesignByCardDesignID(t *testing.T) {
	setMockDataSet()

	t.Run("valid flow", func(t *testing.T) {
		_, err := FetchCardDesignByCardDesignID(context.Background(), "a87d3ed6-2dda-11ed-95fa-cf6eb74e5eee")
		assert.Equal(t, err, nil)
	})
	t.Run("invalid Card Design ID", func(t *testing.T) {
		errorCode := api.ResourceConflict
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "invalid Card Design Id.",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
		_, err := FetchCardDesignByCardDesignID(context.Background(), test.TestInvalidCardDesignID)
		assert.Equal(t, err, expectedErr)
	})
	t.Run("When no designs available", func(t *testing.T) {
		setEmptyMockDataSet()
		_, err := FetchCardDesignByCardDesignID(context.Background(), test.TestInvalidCardDesignID)
		assert.Equal(t, data.ErrNoData, err)
	})
}

func TestFetchAllCardDesigns(t *testing.T) {
	t.Run("valid flow", func(t *testing.T) {
		setMockDataSet()
		cardDesigns, err := FetchAllCardDesigns(context.Background())

		assert.Equal(t, err, nil)
		assert.NotNil(t, cardDesigns)
		assert.True(t, len(cardDesigns) > 0)
	})
	t.Run("invalid flow", func(t *testing.T) {
		setEmptyMockDataSet()
		_, err := FetchAllCardDesigns(context.Background())
		assert.NotNil(t, err)
	})
}

func TestFetchAllCards(t *testing.T) {
	t.Run("valid flow", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		GetUserID = func(ctx context.Context) string {
			return empty
		}

		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{}}, nil)

		_, err := FetchAllCards(context.Background(), userID)
		assert.Equal(t, err, nil)
	})
	t.Run("Error in finding card", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		GetUserID = func(ctx context.Context) string {
			return empty
		}

		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

		_, err := FetchAllCards(context.Background(), userID)
		assert.Equal(t, err, nil)
	})
}

func TestFindCardDesignByCardDesignID(t *testing.T) {
	t.Run("valid flow", func(t *testing.T) {
		setMockDataSet()
		cardDesign, err := findCardDesignByCardDesignID(test.GetMockCardDesigns(), test.TestCardDesignID)

		assert.Equal(t, err, nil)
		assert.NotNil(t, cardDesign)
	})
	t.Run("invalid flow", func(t *testing.T) {
		_, err := findCardDesignByCardDesignID(nil, test.TestCardDesignID)
		assert.NotNil(t, err)
	})
}

func TestUpdateCardDesignStockCount(t *testing.T) {
	setMockDataSet()
	t.Run("valid flow", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		GetUserID = func(ctx context.Context) string {
			return empty
		}

		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{}}, nil)

		err := UpdateCardDesignStockCount(context.Background(), test.TestCardDesignID)
		assert.Equal(t, err, nil)
	})
	t.Run("When card design is empty", func(t *testing.T) {
		setEmptyMockDataSet()
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		GetUserID = func(ctx context.Context) string {
			return empty
		}

		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

		err := UpdateCardDesignStockCount(context.Background(), test.TestCardDesignID)
		assert.NotNil(t, err)
	})
	t.Run("When given card design stock qty is less than 10", func(t *testing.T) {
		setMockDataSet()
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		GetUserID = func(ctx context.Context) string {
			return empty
		}

		errorCode := api.ResourceConflict
		expectedError := &servus.ServiceError{
			Code:     string(errorCode),
			Message:  "Card design is not available.",
			HTTPCode: errorCode.HTTPStatusCode(),
		}

		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)

		err := UpdateCardDesignStockCount(context.Background(), test.TestUnavailableCardDesignID)
		assert.NotNil(t, err)
		assert.Equal(t, expectedError, err)
	})
}

func TestOverwriteCardDesignStockCount(t *testing.T) {
	setMockDataSet()
	t.Run("valid flow", func(t *testing.T) {
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao

		cardDesignObj, err := OverwriteCardDesignStockCount(context.Background(), test.TestCardDesignID, uint64(20))
		assert.Equal(t, err, nil)
		assert.Equal(t, cardDesignObj.StockCount, uint64(20))
	})
}

func TestGetPhysicalCardDeliveryInfoWithPhysicalCard(t *testing.T) {
	deliveryConfig := getPhysicalCardDeliveryConfig()
	card := &storage.Card{
		PhysicalCardOrderedDate: time.Now(),
		OrderStatus:             string(constant.OrderStatusOrdered),
	}

	resp := GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	assert.Equal(t, resp.DeliveryStatus, constant.DeliveryStatusOrderConfirmed)
	assert.Equal(t, resp.CanActivateCard, false)

	card.PhysicalCardOrderedDate = card.PhysicalCardOrderedDate.AddDate(0, 0, -3)
	resp = GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	assert.Equal(t, resp.DeliveryStatus, constant.DeliveryStatusPosted)
	assert.Equal(t, resp.CanActivateCard, true)

	card.PhysicalCardOrderedDate = card.PhysicalCardOrderedDate.AddDate(0, 0, -14)
	resp = GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	assert.Equal(t, resp.DeliveryStatus, constant.DeliveryStatusNotReceived)
	assert.Equal(t, resp.CanActivateCard, true)
	assert.Equal(t, resp.CanViewHelpCenter, true)

	card.PhysicalCardOrderedDate = card.PhysicalCardOrderedDate.AddDate(0, 0, -90)
	resp = GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	assert.Equal(t, resp.DeliveryStatus, constant.DeliveryStatusNotReceived)
	assert.Equal(t, resp.CanActivateCard, true)
	assert.Equal(t, resp.CanView3RdMonthNotification, true)
	assert.Equal(t, resp.CanView9ThMonthNotification, false)

	card.PhysicalCardOrderedDate = card.PhysicalCardOrderedDate.AddDate(0, 0, -300)
	resp = GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	assert.Equal(t, resp.DeliveryStatus, "")
	assert.Equal(t, resp.CanActivateCard, false)
	assert.Equal(t, resp.CanView3RdMonthNotification, false)
	assert.Equal(t, resp.CanView9ThMonthNotification, true)

	card.PhysicalCardOrderedDate = time.Now()
	resp = GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	expectedEstimatedCardArrivalDay := int(deliveryConfig.EstimatedArrivalDays) + int(deliveryConfig.EstimatedArrivalDaysBuffer)
	expectedEstimatedCardArrivalDate := time.Now().AddDate(0, 0, expectedEstimatedCardArrivalDay).Format("2006-01-02")
	assert.Equal(t, resp.EstimatedCardArrivalDate, expectedEstimatedCardArrivalDate)
}

func TestGetPhysicalCardDeliveryInfoWithVirtualCard(t *testing.T) {
	deliveryConfig := getPhysicalCardDeliveryConfig()
	card := &storage.Card{
		OrderStatus: "",
	}

	resp := GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	assert.Equal(t, &api.PhysicalCardDeliveryInfo{}, resp)
}

func TestGetPhysicalCardDeliveryInfoWithPhysicalCardAndIncorrectData(t *testing.T) {
	deliveryConfig := getPhysicalCardDeliveryConfig()
	card := &storage.Card{
		OrderStatus: string(constant.OrderStatusOrdered),
	}

	resp := GetPhysicalCardDeliveryInfo(deliveryConfig, card)
	assert.Equal(t, &api.PhysicalCardDeliveryInfo{}, resp)
}

func TestGetCardType(t *testing.T) {
	card := &storage.Card{
		OrderStatus: string(constant.OrderStatusActivated),
		SetPinAt:    time.Now(),
	}

	resp := GetCardType(card)
	assert.Equal(t, "PHYSICAL", resp)

	card = &storage.Card{
		OrderStatus: "",
	}

	resp = GetCardType(card)
	assert.Equal(t, "VIRTUAL", resp)
}

func TestGetOrderStatus(t *testing.T) {
	card := &storage.Card{OrderStatus: string(constant.OrderStatusActivated)}
	resp := GetOrderStatus(card)
	assert.Equal(t, string(constant.OrderStatusOrdered), resp)

	card.SetPinAt = time.Now()
	resp = GetOrderStatus(card)
	assert.Equal(t, string(constant.OrderStatusActivated), resp)

	card.OrderStatus = constant.DeliveryStatusProcessing
	resp = GetOrderStatus(card)
	assert.Equal(t, string(constant.OrderStatusProcessing), resp)
}

func TestFetchAvailableCardDesigns(t *testing.T) {
	t.Run("when at least one card design is available", func(t *testing.T) {
		setMockDataSet()
		cardDesigns, err := FetchAvailableCardDesigns(context.Background())

		assert.Equal(t, err, nil)
		assert.NotNil(t, cardDesigns)
		assert.True(t, len(cardDesigns) > 0)
	})
	t.Run("when no card design is available", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()

		test.SetMockUnavailableDataForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)

		cardDesigns, err := FetchAvailableCardDesigns(context.Background())

		assert.Equal(t, err, nil)
		assert.Nil(t, cardDesigns)
		assert.True(t, len(cardDesigns) == 0)
	})
	t.Run("invalid flow", func(t *testing.T) {
		setEmptyMockDataSet()
		_, err := FetchAllCardDesigns(context.Background())
		assert.NotNil(t, err)
	})
}

func TestFetchCardByCardAndUserID(t *testing.T) {
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO
	t.Run("Valid flow", func(t *testing.T) {
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
			OrderStatus:             "ORDERED",
			PhysicalCardOrderedDate: time.Now().AddDate(0, 0, -3),
		}}, nil).Once()
		card, err := FetchCardByCardAndUserID(context.Background(), userID, "1234")
		assert.Nil(t, err)
		assert.NotNil(t, card)
	})
	t.Run("In Valid flow", func(t *testing.T) {
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		card, err := FetchCardByCardAndUserID(context.Background(), userID, "1234")
		assert.NotNil(t, err)
		assert.Nil(t, card)
	})
}

func TestGenerateAlphaNumericCode(t *testing.T) {
	t.Run("Valid flow", func(t *testing.T) {
		code := GenerateAlphaNumericCode(constant.ActivationCodeLength)
		assert.NotNil(t, code)
		assert.Equal(t, constant.ActivationCodeLength, len(code))
		isAlphanumeric := regexp.MustCompile(constant.ActivationCodeRegex).MatchString(code)
		assert.True(t, isAlphanumeric)
	})
}

func getPhysicalCardDeliveryConfig() config.PhysicalCardDeliveryConfig {
	deliveryConfig := config.PhysicalCardDeliveryConfig{
		OrderConfirmed:             1,
		Posted:                     13,
		NotReceivedYet:             14,
		CanActivateCard:            2,
		CanViewHelpCenter:          14,
		EstimatedArrivalDays:       10,
		EstimatedArrivalDaysBuffer: 2,
	}
	return deliveryConfig
}

func setMockDataSet() {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)
}

func setEmptyMockDataSet() {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetEmptyMockForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)
}

func TestFetchCardByCardOrderedAt(t *testing.T) {
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO

	t.Run("Valid flow", func(t *testing.T) {
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{
			OrderStatus:             "ORDERED",
			PhysicalCardOrderedDate: time.Now().AddDate(0, 0, -3),
		}}, nil).Once()
		card, err := FetchCardByCardOrderedAt(context.Background(), "MY")
		assert.Nil(t, err)
		assert.NotNil(t, card)
	})

	t.Run("In valid flow", func(t *testing.T) {
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		card, err := FetchCardByCardOrderedAt(context.Background(), "MY")
		assert.NotNil(t, err)
		assert.Nil(t, card)
	})
}

func TestValidateToggleAction(t *testing.T) {
	scenarios := []struct {
		desc        string
		cardStatus  constant.CardStatus
		orderStatus constant.OrderStatus
		toggleType  string
		expected    bool
	}{
		{
			desc:        "active virtual card - cnp",
			cardStatus:  constant.CardStatusActive,
			orderStatus: constant.OrderStatusNo,
			toggleType:  constant.CnpTXN,
			expected:    true,
		},
		{
			desc:        "active virtual card - overseas pos",
			cardStatus:  constant.CardStatusActive,
			orderStatus: constant.OrderStatusNo,
			toggleType:  constant.OverseasPOSTXN,
			expected:    false,
		},
		{
			desc:        "active ordered physical card - cnp",
			cardStatus:  constant.CardStatusActive,
			orderStatus: constant.OrderStatusOrdered,
			toggleType:  constant.CnpTXN,
			expected:    true,
		},
		{
			desc:        "active ordered physical card - overseas pos",
			cardStatus:  constant.CardStatusActive,
			orderStatus: constant.OrderStatusOrdered,
			toggleType:  constant.OverseasPOSTXN,
			expected:    false,
		},
		{
			desc:        "active activated physical card - cnp",
			cardStatus:  constant.CardStatusActive,
			orderStatus: constant.OrderStatusActivated,
			toggleType:  constant.CnpTXN,
			expected:    true,
		},
		{
			desc:        "active activated physical card - overseas pos",
			cardStatus:  constant.CardStatusActive,
			orderStatus: constant.OrderStatusActivated,
			toggleType:  constant.OverseasPOSTXN,
			expected:    true,
		},
		{
			desc:        "locked virtual card - cnp",
			cardStatus:  constant.CardStatusLocked,
			orderStatus: constant.OrderStatusNo,
			toggleType:  constant.CnpTXN,
			expected:    true,
		},
		{
			desc:        "locked virtual card - overseas pos",
			cardStatus:  constant.CardStatusLocked,
			orderStatus: constant.OrderStatusNo,
			toggleType:  constant.OverseasPOSTXN,
			expected:    false,
		},
		{
			desc:        "locked activated physical card - cnp",
			cardStatus:  constant.CardStatusLocked,
			orderStatus: constant.OrderStatusActivated,
			toggleType:  constant.CnpTXN,
			expected:    true,
		},
		{
			desc:        "locked activated physical card - overseas pos",
			cardStatus:  constant.CardStatusLocked,
			orderStatus: constant.OrderStatusActivated,
			toggleType:  constant.OverseasPOSTXN,
			expected:    true,
		},
		{
			desc:        "processing card - cnp",
			cardStatus:  constant.CardStatusProcessing,
			orderStatus: constant.OrderStatusProcessing,
			toggleType:  constant.CnpTXN,
			expected:    false,
		},
		{
			desc:        "processing card - overseas pos",
			cardStatus:  constant.CardStatusProcessing,
			orderStatus: constant.OrderStatusProcessing,
			toggleType:  constant.OverseasPOSTXN,
			expected:    false,
		},
		{
			desc:        "retired card - cnp",
			cardStatus:  constant.CardStatusRetired,
			orderStatus: constant.OrderStatusProcessing,
			toggleType:  constant.CnpTXN,
			expected:    false,
		},
		{
			desc:        "retired card - overseas pos",
			cardStatus:  constant.CardStatusRetired,
			orderStatus: constant.OrderStatusActivated,
			toggleType:  constant.OverseasPOSTXN,
			expected:    false,
		},
		{
			desc:        "failed card - cnp",
			cardStatus:  constant.CardStatusFailed,
			orderStatus: constant.OrderStatusProcessing,
			toggleType:  constant.CnpTXN,
			expected:    false,
		},
		{
			desc:        "failed card - overseas pos",
			cardStatus:  constant.CardStatusFailed,
			orderStatus: constant.OrderStatusProcessing,
			toggleType:  constant.OverseasPOSTXN,
			expected:    false,
		},
	}
	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			result := ValidateCardTxnToggleAction(scenario.cardStatus, scenario.orderStatus, scenario.toggleType)
			assert.Equal(t, scenario.expected, result)
		})
	}
}

func TestResolveUserName(t *testing.T) {
	t.Run("success flow", func(t *testing.T) {
		customerData := &customerMasterAPI.Customer{
			PreferredName: &preferredName,
			Name:          &username,
		}
		customerBytes, _ := json.Marshal(customerData)
		testGetCustomerResponse := &customerMasterAPI.GetCustomerResponse{Customer: &customerMasterAPI.CustomerData{Data: customerBytes}}

		mockCustomerMasterClient := &mockCustomerMaster.CustomerMaster{}
		mockCustomerMasterClient.On("GetCustomer", mock.Anything, mock.Anything).Return(testGetCustomerResponse, nil).Once()
		result := ResolveUserName(context.Background(), "12345", mockCustomerMasterClient)
		assert.Equal(t, preferredName, result)
	})

	t.Run("customer master error", func(t *testing.T) {
		mockCustomerMasterClient := &mockCustomerMaster.CustomerMaster{}
		mockCustomerMasterClient.On("GetCustomer", mock.Anything, mock.Anything).Return(nil, errors.New("something went wrong")).Once()
		result := ResolveUserName(context.Background(), "12345", mockCustomerMasterClient)
		assert.Equal(t, "", result)
	})

	t.Run("json unmarshal error", func(t *testing.T) {
		customerBytes, _ := json.Marshal("")
		testGetCustomerResponse := &customerMasterAPI.GetCustomerResponse{Customer: &customerMasterAPI.CustomerData{Data: customerBytes}}

		mockCustomerMasterClient := &mockCustomerMaster.CustomerMaster{}
		mockCustomerMasterClient.On("GetCustomer", mock.Anything, mock.Anything).Return(testGetCustomerResponse, nil).Once()
		result := ResolveUserName(context.Background(), "12345", mockCustomerMasterClient)
		assert.Equal(t, "", result)
	})

	t.Run("empty preferred name", func(t *testing.T) {
		customerData := &customerMasterAPI.Customer{
			PreferredName: nil,
			Name:          &username,
		}
		customerBytes, _ := json.Marshal(customerData)
		testGetCustomerResponse := &customerMasterAPI.GetCustomerResponse{Customer: &customerMasterAPI.CustomerData{Data: customerBytes}}

		mockCustomerMasterClient := &mockCustomerMaster.CustomerMaster{}
		mockCustomerMasterClient.On("GetCustomer", mock.Anything, mock.Anything).Return(testGetCustomerResponse, nil).Once()
		result := ResolveUserName(context.Background(), "12345", mockCustomerMasterClient)
		assert.Equal(t, username, result)
	})
}

func TestGenerateTrackerUrl(t *testing.T) {
	const (
		cardID = "1234"
	)
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO
	mockConfig := config.PhysicalCardDeliveryConfig{
		OrderConfirmed: 1,
		CourierList:    map[string]string{"City-Link": "https://www.citylinkexpress.com/tracking-result/?track0=consignmentNo"},
	}

	scenarios := []struct {
		postScript []byte
		desc       string
		expected   *api.DeliveryTracker
	}{
		{
			desc:       "consignment number available",
			postScript: []byte(`{"ConsignmentNumber": "1234", "CourierName": "City-Link"}`),
			expected: &api.DeliveryTracker{
				CourierName: "City-Link",
				TrackerUrl:  "https://www.citylinkexpress.com/tracking-result/?track0=1234",
			},
		},
		{
			desc:       "consignment number NOT available",
			postScript: []byte(``),
			expected:   nil,
		},
	}
	for _, _scn := range scenarios {
		scenario := _scn
		t.Run(scenario.desc, func(t *testing.T) {
			result := GenerateTrackerUrl(mockConfig, &storage.Card{
				PostScript: scenario.postScript,
			})
			assert.Equal(t, scenario.expected, result)
		})
	}
}
