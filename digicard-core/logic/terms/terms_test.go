package terms

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	mockCustomerMaster "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

func TestClientImpl_PersistCardsTermsAcceptance(t *testing.T) {
	mockCustomerMasterClient := mockCustomerMaster.CustomerMaster{}
	goodCtx := commonCtx.WithUserID(context.Background(), "testID")
	tests := []struct {
		name     string
		ctx      context.Context
		mockfunc func()
		wantErr  error
	}{
		{
			name: "Happy path",
			ctx:  goodCtx,
			mockfunc: func() {
				mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
			wantErr: nil,
		},
		{
			name: "cm error",
			ctx:  goodCtx,
			mockfunc: func() {
				mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, errors.New("some error")).Once()
			},
			wantErr: api.DefaultInternalServerError,
		},
	}
	c := &ClientImpl{
		StatsD:               statsd.NewNoop(),
		CustomerMasterClient: &mockCustomerMasterClient,
	}
	for _, tt := range tests {
		tt := tt
		tt.mockfunc()
		t.Run(tt.name, func(t *testing.T) {
			err := c.PersistCardsTermsAcceptance(tt.ctx, nil) // Use nil for backward compatibility
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func TestClientImpl_PersistCardsTermsAcceptanceWithProductConfig(t *testing.T) {
	mockCustomerMasterClient := mockCustomerMaster.CustomerMaster{}
	goodCtx := commonCtx.WithUserID(context.Background(), "testID")

	t.Run("CCC product config uses CCC agreement ID", func(t *testing.T) {
		productConfig := &constant.ProductConfig{
			ProductVariant: constant.MYCreditCard,
			TncAgreementID: "termsAndConditions_CreditCard_Issuance",
		}

		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.MatchedBy(func(req interface{}) bool {
			// We can't easily inspect the exact content, but we verify the call is made
			return true
		})).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, productConfig)
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("debit product config uses debit agreement ID", func(t *testing.T) {
		productConfig := &constant.ProductConfig{
			ProductVariant: constant.MYDebitCard,
			TncAgreementID: "termsAndConditions_Cards_Issuance",
		}

		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, productConfig)
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("nil product config uses default agreement ID", func(t *testing.T) {
		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, nil)
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("empty agreement ID in config falls back to default", func(t *testing.T) {
		productConfig := &constant.ProductConfig{
			ProductVariant: constant.MYCreditCard,
			TncAgreementID: "", // Empty agreement ID should trigger fallback
		}

		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, nil).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, productConfig)
		assert.NoError(t, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})

	t.Run("customer master error with product config", func(t *testing.T) {
		productConfig := &constant.ProductConfig{
			ProductVariant: constant.MYCreditCard,
			TncAgreementID: "termsAndConditions_CreditCard_Issuance",
		}

		expectedError := errors.New("customer master error")
		mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, expectedError).Once()

		c := &ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
		}

		err := c.PersistCardsTermsAcceptance(goodCtx, productConfig)
		assert.Error(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		mockCustomerMasterClient.AssertExpectations(t)
	})
}
