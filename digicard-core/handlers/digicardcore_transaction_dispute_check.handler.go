package handlers

import (
	context "context"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
)

const (
	logDisputeTag = "transaction_dispute_check"
)

// TransactionDisputeCheck ...
func (d *DigicardCoreService) TransactionDisputeCheck(ctx context.Context, req *api.TransactionDisputeCheckRequest) (*api.TransactionDisputeCheckResponse, error) {
	userID := logic.GetUserID(ctx)
	err := validateTransactionDisputeCheckRequest(req, userID)
	if err != nil {
		slog.FromContext(ctx).Warn(logDisputeTag, "request validation failed", slog.Error(err))
		return nil, err
	}

	return d.DisputeCheckClient.DisputeCheck(ctx, req)
}

func validateTransactionDisputeCheckRequest(req *api.TransactionDisputeCheckRequest, userID string) error {
	if req == nil {
		return logic.BuildErrorResponse(api.BadRequest, "Invalid Request")
	}

	if userID == "" || req.TransactionID == "" {
		return logic.BuildErrorResponse(api.BadRequest, "CustomerID and Transaction ID is mandatory")
	}
	return nil
}
