package handlers

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/recorder"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/common"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/utils/cache"
	"gitlab.myteksi.net/dakota/common/redis"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2"
	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"
)

const (
	createCardHdlrLogTag      string = "create_card_handler"
	createCardRedisLockPrefix        = "CreateCardLock:"
)

// CreateCard is to create new card for a specific user
func (d *DigicardCoreService) CreateCard(ctx context.Context, req *api.CreateCardRequest) (*api.CardResponse, error) {
	// This function is used to prevent context canceled from front end in short amount of time,
	// and cause the data in our side and 3rd party card provider is not sync
	ctx = bgcontext.BackgroundWithValue(ctx)
	var cancel context.CancelFunc
	ctx, cancel = context.WithTimeout(ctx, d.TimeoutConfig.Timeout)
	defer cancel()

	recorder.RecordApiFuncName(ctx, common.CreateCard)
	if commonCtx.GetUserID(ctx) == "" {
		errorCode := api.Forbidden
		recorder.RecordResponseCode(ctx, string(errorCode))
		return nil, servus.ServiceError{
			Code:     string(errorCode),
			Message:  "'X-Grab-Id-Userid' is missing.",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
	}
	if errs := createCardRequestParamsValidator(req); len(errs) != 0 {
		errorCode := api.BadRequest
		recorder.RecordResponseCode(ctx, string(errorCode))
		return nil, servus.ServiceError{
			Code:     string(errorCode),
			Message:  "request has invalid parameter(s) or header(s).",
			HTTPCode: errorCode.HTTPStatusCode(),
			Errors:   errs,
		}
	}

	if !d.WhitelistClient.IsUserWhitelisted(ctx, commonCtx.GetUserID(ctx)) {
		errorCode := api.Forbidden
		recorder.RecordResponseCode(ctx, string(errorCode))
		return nil, servus.ServiceError{
			Code:     string(errorCode),
			Message:  "user is not whitelisted to create card",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
	}

	// Get product variant from account service
	accountReq := &accountAPIV2.GetAccountRequest{AccountID: req.AccountID, FetchBalance: false}
	accountResp, err := d.AccountServiceClientV2.GetAccountDetailsByAccountID(ctx, accountReq)
	if err != nil {
		recorder.RecordResponseCode(ctx, err.Error())
		return nil, err
	}

	if err := d.AccountVerifyClient.VerifyAccount(ctx, req.AccountID); err != nil {
		recorder.RecordResponseCode(ctx, err.Error())
		return nil, err
	}

	productVariant := constant.ProductVariant(accountResp.Account.ProductVariantID)
	productConfig := constant.GetProductConfig(productVariant)

	userID := logic.GetUserID(ctx)
	lockKey := createCardRedisLockPrefix + userID
	lock, errLock := cache.RedisClient.TryLock(ctx, lockKey, time.Duration(3)*time.Second)
	if errLock == redis.ErrLockOccupied {
		slog.FromContext(ctx).Warn(createCardHdlrLogTag, fmt.Sprintf("not able to obtain lock for Card Creation: %s", lockKey), slog.Error(errLock))
		errorCode := api.TooManyRequest
		return nil, servus.ServiceError{
			HTTPCode: errorCode.HTTPStatusCode(),
			Code:     string(errorCode),
			Message:  "Too Many Requests",
		}
	}

	if errLock != nil {
		slog.FromContext(ctx).Warn(createCardHdlrLogTag, fmt.Sprintf("Issues while obtaining lock %s", lockKey), slog.Error(errLock))
		return nil, api.DefaultInternalServerError
	}

	defer func() {
		_ = lock.Unlock(ctx)
	}()

	if err := d.CardsTermsClient.PersistCardsTermsAcceptance(ctx, productConfig); err != nil {
		recorder.RecordResponseCode(ctx, api.DefaultInternalServerError.Code)
		return nil, api.DefaultInternalServerError
	}

	var res *api.CardResponse
	var exeErr error
	if req.Type == string(constant.PhysicalCard) {
		res, exeErr = d.PhysicalCardOrderClient.PhysicalCardOrder(ctx, req)
	} else {
		res, exeErr = d.CreateCardWorkflow.ExecuteCreateCardWorkflow(ctx, req, productConfig)
	}

	d.publishCardCreationLog(ctx, req, res, exeErr)
	if exeErr != nil {
		recorder.RecordResponseCode(ctx, exeErr.Error())
	} else if res != nil {
		recorder.RecordResponseCode(ctx, res.StatusReason)
	}
	return res, exeErr
}

func createCardRequestParamsValidator(req *api.CreateCardRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail

	emptyFieldValidator(req.IdempotencyKey, "idempotencyKey", &errs)
	emptyFieldValidator(req.AccountID, "accountID", &errs)
	emptyFieldValidator(req.CardDesignId, "cardDesignID", &errs)

	if req.Type != string(constant.PhysicalCard) && req.Type != string(constant.VirtualCard) {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldInvalid),
			Message:   "invalid Type",
			Path:      "type",
		})
	}

	if req.Type == string(constant.VirtualCard) {
		emptyFieldValidator(req.DisplayName, "displayName", &errs)
		if req.DisplayName != "" && !isValidDisplayName(req.DisplayName) {
			errs = append(errs, servus.ErrorDetail{
				ErrorCode: string(api.FieldInvalid),
				Message:   "invalid Display Name",
				Path:      "displayName",
			})
		}
	}

	if req.Type == string(constant.PhysicalCard) {
		emptyFieldValidator(req.CardID, "cardID", &errs)
	}

	return errs
}

func (d *DigicardCoreService) publishCardCreationLog(ctx context.Context, req *api.CreateCardRequest, res *api.CardResponse, activityErr error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.CardActivityTag, "CreateCard"))
	cardCreateActivity := audit.CardActivityAuditRequest{
		Status: customerjournalapi.AuditEventSuccess,
		Event:  customerjournalapi.CardVirtualCreationEvent,
		Source: customerjournalapi.TriggeredByGXBankAppSource,
	}

	if req.Type == string(constant.PhysicalCard) {
		cardCreateActivity.Event = customerjournalapi.CardPhysicalCreationEvent
	}

	if activityErr != nil { // failed event
		cardCreateActivity.Status = customerjournalapi.AuditEventFailed
		cardCreateActivity.FailedReason = activityErr.Error()
	}

	if res != nil && res.Status != constant.Success {
		cardCreateActivity.Status = customerjournalapi.AuditEventFailed
		cardCreateActivity.FailedReason = res.StatusReason
	}
	// publish virtual card activation
	d.CardActivityLog.SendActivityLog(ctx, cardCreateActivity)

	// publish card cnp txn toggle(if any)
	if req.Type == string(constant.VirtualCard) && activityErr == nil {
		beforeValue, afterValue := resolveBeforeAfterValueCnpToggle(req.CnpEnabled)
		cardCNPEnableActivity := audit.CardActivityAuditRequest{
			Status:      customerjournalapi.AuditEventSuccess,
			Event:       customerjournalapi.CardOnboardOnlineTxnToggleEvent,
			Source:      customerjournalapi.TriggeredByGXBankAppSource,
			BeforeValue: beforeValue,
			AfterValue:  afterValue,
		}

		d.CardActivityLog.SendActivityLog(ctx, cardCNPEnableActivity)
	}
}

func resolveBeforeAfterValueCnpToggle(isEnabled bool) (map[string]interface{}, map[string]interface{}) {
	if isEnabled {
		return map[string]interface{}{audit.CnpToggleDesc: constant.ToggleDisabled}, map[string]interface{}{audit.CnpToggleDesc: constant.ToggleEnabled}
	}

	return map[string]interface{}{audit.CnpToggleDesc: constant.ToggleDisabled}, map[string]interface{}{audit.CnpToggleDesc: constant.ToggleDisabled}
}
