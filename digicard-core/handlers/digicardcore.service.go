package handlers

import (
	transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/acs/otp"
	mailingaddress "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/address/mailing"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/activate"
	activeandsetpin "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/active_and_set_pin"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/block"
	createCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/create"
	getAllDesigns "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/get_all_designs"
	updateCardStock "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/update_card_stock"
	getAllCards "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_all_cards"
	getCardDetailsWithEncryption "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_card_details_with_encryption"
	getCardsByCustomer "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_cards_by_customer"
	getSpecificCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/get_specific_card"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/namechecker"
	pinmanager "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/pin_manager"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/toggle"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/unblock"
	updateCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/update"
	updatecardstatus "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/update_card_status"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/enquiry"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/terms"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/whitelist"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
)

// DigicardCoreService serves as the context for handlers.
type DigicardCoreService struct {
	CreateCardWorkflow                 *createCard.WorkflowImpl             `inject:"createCardWorkflow"`
	UpdateCardWorkflow                 *updateCard.WorkflowImpl             `inject:"updateCardWorkflow"`
	CardActivateAndSetPinWorkFlow      activeandsetpin.Workflow             `inject:"activeAndSetPinWorkflow"`
	GetAllCardsClient                  getAllCards.Client                   `inject:"client.GetAllCards"`
	GetSpecificCardClient              getSpecificCard.Client               `inject:"client.GetSpecificCard"`
	GetCardDetailsWithEncryptionClient getCardDetailsWithEncryption.Client  `inject:"client.GetCardDetailsWithEncryption"`
	UpdateCardStock                    updateCardStock.Client               `inject:"client.updateCardStock"`
	StatusEnquiryClient                enquiry.Client                       `inject:"client.Enquiry"`
	AcsTriggerOtp                      otp.Client                           `inject:"client.acsTriggerOtp"`
	GetAllCardDesignsClient            getAllDesigns.Client                 `inject:"client.GetAllCardDesigns"`
	LimitConfigs                       []config.LimitConfig                 `inject:"config.limits"`
	TransactionLimitClient             transactionLimitAPI.TransactionLimit `inject:"client.transactionLimit"`
	GetCardsDetailByCustomer           getCardsByCustomer.Client            `inject:"client.GetCardsByCustomer"`
	PhysicalCardDeliveryConfig         config.PhysicalCardDeliveryConfig    `inject:"config.physicalCardDelivery"`
	OverseasToggleUpdate               toggle.Client                        `inject:"client.toggleUpdate"`
	DisputeCheckClient                 transaction.Client                   `inject:"client.disputeCheck"`
	MailingAddressClient               mailingaddress.Client                `inject:"client.mailingAddress"`
	CardsTermsClient                   terms.Client                         `inject:"client.cardsTerms"`
	PhysicalCardActivateClient         activate.PhysicalCardActivateClient  `inject:"client.physicalCardActivate"`
	BankBlockClient                    block.BankBlockClient                `inject:"client.bankBlock"`
	BankUnblockClient                  unblock.BankUnblockClient            `inject:"client.bankUnblock"`
	UpdateCardStatusClient             updatecardstatus.Client              `inject:"client.updateCardStatus"`
	FeatureFlags                       *config.FeatureFlags                 `inject:"config.featureFlags"`
	PhysicalCardOrderClient            createCard.PhysicalCardOrderClient   `inject:"client.physicalCardOrder"`
	AccountVerifyClient                createCard.AccountVerifyClient       `inject:"client.accountVerify"`
	CnpToggleUpdateClient              toggle.Client                        `inject:"client.cnpToggleUpdate"`
	CardPinManagerClient               pinmanager.CardPinManagerClient      `inject:"client.cardPinManager"`
	CountryConfig                      config.CountryConfig                 `inject:"config.countryConfig"`
	WhitelistClient                    whitelist.Client                     `inject:"client.whitelist"`
	NameCheckerClient                  namechecker.Client                   `inject:"client.nameChecker,optional"`
	CardActivityLog                    audit.CardActivityLogService         `inject:"client.activityLog"`
	TimeoutConfig                      config.TimeoutConfig                 `inject:"config.timeoutConfig"`
	PhysicalCardOrderFee               config.PhysicalCardOrderFee          `inject:"config.physicalCardOrderFee"`
	AccountServiceClientV2             accountAPIV2.AccountService          `inject:"client.accountServiceV2"`
}
