package handlers

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
)

// TriggerOtp triggers the otp for acs validation
func (d *DigicardCoreService) TriggerOtp(ctx context.Context, req *api.TriggerOtpRequest) (*api.TriggerOtpResponse, error) {
	// TODO: complete this handler with business logic.
	// Add validation etc...
	resp := &api.TriggerOtpResponse{Success: true}
	err := d.AcsTriggerOtp.TriggerOtp(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
