package handlers

import (
	"context"
	"testing"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.myteksi.net/dakota/common/redis/mocks"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/terms"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/whitelist"

	"github.com/stretchr/testify/mock"

	createcard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/create"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
)

func TestCreateCardRequestParamsValidator(t *testing.T) {
	t.Run("Empty Field Validator", func(t *testing.T) {
		req := &api.CreateCardRequest{}
		errs := createCardRequestParamsValidator(req)

		idempotencyKeyValidationErr := servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'idempotencyKey' is missing",
			Path:      "idempotencyKey",
		}
		assert.NotNil(t, errs)
		assert.Equal(t, idempotencyKeyValidationErr, errs[0])
	})

	t.Run("Card Type Validator", func(t *testing.T) {
		req := &api.CreateCardRequest{IdempotencyKey: "1234", AccountID: "1234", CardDesignId: "1234"}
		errs := createCardRequestParamsValidator(req)

		typeValidationErr := servus.ErrorDetail{
			ErrorCode: string(api.FieldInvalid),
			Message:   "invalid Type",
			Path:      "type",
		}
		assert.NotNil(t, errs)
		assert.Equal(t, typeValidationErr, errs[0])
	})

	t.Run("Virtual Card Validator without displayName", func(t *testing.T) {
		req := &api.CreateCardRequest{IdempotencyKey: "1234", AccountID: "1234", CardDesignId: "1234", Type: "VIRTUAL"}
		errs := createCardRequestParamsValidator(req)

		displayNameValidationErr := servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'displayName' is missing",
			Path:      "displayName",
		}

		assert.NotNil(t, errs)
		assert.Equal(t, displayNameValidationErr, errs[0])
	})

	t.Run("Virtual Card Validator with displayName", func(t *testing.T) {
		req := &api.CreateCardRequest{IdempotencyKey: "1234", AccountID: "1234", CardDesignId: "1234", Type: "VIRTUAL", DisplayName: "  "}
		errs := createCardRequestParamsValidator(req)

		displayNameValidationErr := servus.ErrorDetail{
			ErrorCode: string(api.FieldInvalid),
			Message:   "invalid Display Name",
			Path:      "displayName",
		}

		assert.NotNil(t, errs)
		assert.Equal(t, displayNameValidationErr, errs[0])
	})

	t.Run("Virtual Card Validator with special character displayName", func(t *testing.T) {
		req := &api.CreateCardRequest{IdempotencyKey: "1234", AccountID: "1234", CardDesignId: "1234", Type: "VIRTUAL", DisplayName: "Test_**"}
		errs := createCardRequestParamsValidator(req)

		displayNameValidationErr := servus.ErrorDetail{
			ErrorCode: string(api.FieldInvalid),
			Message:   "invalid Display Name",
			Path:      "displayName",
		}

		assert.NotNil(t, errs)
		assert.Equal(t, displayNameValidationErr, errs[0])
	})

	t.Run("Virtual Card Validator with long displayName", func(t *testing.T) {
		name := "123456789_123456789_1234"
		req := &api.CreateCardRequest{IdempotencyKey: "1234", AccountID: "1234", CardDesignId: "1234", Type: "VIRTUAL", DisplayName: name}
		errs := createCardRequestParamsValidator(req)

		displayNameValidationErr := servus.ErrorDetail{
			ErrorCode: string(api.FieldInvalid),
			Message:   "invalid Display Name",
			Path:      "displayName",
		}

		assert.NotNil(t, errs)
		assert.Equal(t, displayNameValidationErr, errs[0])
	})

	t.Run("Physical Card Validator", func(t *testing.T) {
		req := &api.CreateCardRequest{IdempotencyKey: "1234", AccountID: "1234", CardDesignId: "1234", Type: "PHYSICAL", DisplayName: "TEST"}
		errs := createCardRequestParamsValidator(req)

		displayNameValidationErr := servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'cardID' is missing",
			Path:      "cardID",
		}

		assert.NotNil(t, errs)
		assert.Equal(t, displayNameValidationErr, errs[0])
	})
}

func TestCreateCard(t *testing.T) {
	mockAccountVerifyClient := createcard.MockAccountVerifyClient{}
	mockCardTermClient := terms.MockClient{}
	mockWhitelistClient := whitelist.MockClient{}

	service := &DigicardCoreService{
		AccountVerifyClient: &mockAccountVerifyClient,
		CardsTermsClient:    &mockCardTermClient,
		WhitelistClient:     &mockWhitelistClient,
	}

	t.Run("Empty Context Validator", func(t *testing.T) {
		req := &api.CreateCardRequest{}
		errorCode := api.Forbidden
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "'X-Grab-Id-Userid' is missing.",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
		_, err := service.CreateCard(context.Background(), req)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})

	t.Run("Empty Request Validator", func(t *testing.T) {
		req := &api.CreateCardRequest{}
		ctx := context.Background()
		ctx = commonCtx.WithUserID(ctx, "1234")
		_, err := service.CreateCard(ctx, req)
		assert.NotNil(t, err)
	})

	t.Run("Error when create term and condition", func(t *testing.T) {
		productConfig := &constant.ProductConfig{
			ProductVariant:   constant.MYDebitCard,
			VendorIdentifier: "MDP",
			TncAgreementID:   string(constant.CardsTermsAgreementID),
			TxnLimitNames: constant.TxnLimits{
				Contactless: "contactless_limit",
				Online:      "online_limit",
				PinAndPay:   "pinpay_limit",
				ATM:         "atm_limit",
			},
		}
		req := &api.CreateCardRequest{
			AccountID:      "AccountID",
			DisplayName:    "DisplayName",
			Type:           "VIRTUAL",
			IdempotencyKey: "IdempotencyKey",
			CardID:         "CardID",
			CardDesignId:   "CardDesignId",
			CnpEnabled:     false,
		}
		expectedErr := api.DefaultInternalServerError
		mockRedis := test.SetMockRedisClient()
		mockLock := &mocks.Lock{}
		mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil)
		mockLock.On("Unlock", mock.Anything).Return(nil)
		mockCardTermClient.On("PersistCardsTermsAcceptance", mock.Anything).Return(expectedErr).Once()
		mockAccountVerifyClient.On("VerifyAccount", mock.Anything, req.AccountID, string(productConfig.ProductVariant)).Return(nil).Once()
		mockWhitelistClient.On("IsUserWhitelisted", mock.Anything, mock.Anything).Return(true).Once()

		ctx := commonCtx.WithUserID(context.Background(), "reqID")

		_, err := service.CreateCard(ctx, req)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})

	t.Run("Timeout error with new context enabled", func(t *testing.T) {
		service = &DigicardCoreService{
			AccountVerifyClient: &mockAccountVerifyClient,
			WhitelistClient:     &mockWhitelistClient,
			TimeoutConfig:       config.TimeoutConfig{Timeout: 1 * time.Millisecond},
		}
		req := &api.CreateCardRequest{
			AccountID:      "AccountID",
			DisplayName:    "DisplayName",
			Type:           "VIRTUAL",
			IdempotencyKey: "IdempotencyKey",
			CardID:         "CardID",
			CardDesignId:   "CardDesignId",
			CnpEnabled:     false,
		}

		mockWhitelistClient.
			On("IsUserWhitelisted", mock.Anything, mock.Anything).
			Return(true).Once()

		mockAccountVerifyClient.On("VerifyAccount", mock.Anything, req.AccountID).Return(
			func(ctx context.Context, accountID string) error {
				time.Sleep(100 * time.Millisecond)
				return ctx.Err()
			}).Once()
		expectedErr := context.DeadlineExceeded
		ctx := commonCtx.WithUserID(context.Background(), "reqID")
		_, err := service.CreateCard(ctx, req)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestCardActivityAuditLog(t *testing.T) {
	t.Run("should log virtual card creation success", func(t *testing.T) {
		cardAuditLogService := &audit.MockCardActivityLogService{}
		service := &DigicardCoreService{
			CardActivityLog: cardAuditLogService,
		}
		ctx := commonCtx.WithUserID(context.Background(), uuid.NewString())
		req := &api.CreateCardRequest{
			AccountID:      "AccountID",
			DisplayName:    "DisplayName",
			Type:           "VIRTUAL",
			IdempotencyKey: "IdempotencyKey",
			CardID:         "CardID",
			CardDesignId:   "CardDesignId",
			CnpEnabled:     false,
		}
		res := &api.CardResponse{
			Status: constant.Success,
		}
		cardAuditLogService.On("SendActivityLog", mock.Anything, createVirtualCardCreationSuccessEvent()).Once()
		cardAuditLogService.On("SendActivityLog", mock.Anything, createCnpDisableSuccessEvent()).Once()
		service.publishCardCreationLog(ctx, req, res, nil)
		cardAuditLogService.AssertNumberOfCalls(t, "SendActivityLog", 2)
	})
	t.Run("should log virtual card creation(enable CNP) success", func(t *testing.T) {
		cardAuditLogService := &audit.MockCardActivityLogService{}
		service := &DigicardCoreService{
			CardActivityLog: cardAuditLogService,
		}
		ctx := commonCtx.WithUserID(context.Background(), uuid.NewString())
		req := &api.CreateCardRequest{
			AccountID:      "AccountID",
			DisplayName:    "DisplayName",
			Type:           "VIRTUAL",
			IdempotencyKey: "IdempotencyKey",
			CardID:         "CardID",
			CardDesignId:   "CardDesignId",
			CnpEnabled:     true,
		}
		res := &api.CardResponse{
			Status: constant.Success,
		}
		cardAuditLogService.On("SendActivityLog", mock.Anything, createVirtualCardCreationSuccessEvent()).Once()
		cardAuditLogService.On("SendActivityLog", mock.Anything, createCnpEnableSuccessEvent()).Once()
		service.publishCardCreationLog(ctx, req, res, nil)
		cardAuditLogService.AssertNumberOfCalls(t, "SendActivityLog", 2)
	})
	t.Run("should log virtual card creation failed", func(t *testing.T) {
		cardAuditLogService := &audit.MockCardActivityLogService{}
		service := &DigicardCoreService{
			CardActivityLog: cardAuditLogService,
		}
		ctx := commonCtx.WithUserID(context.Background(), uuid.NewString())
		req := &api.CreateCardRequest{
			AccountID:      "AccountID",
			DisplayName:    "DisplayName",
			Type:           "VIRTUAL",
			IdempotencyKey: "IdempotencyKey",
			CardID:         "CardID",
			CardDesignId:   "CardDesignId",
			CnpEnabled:     false,
		}
		res := &api.CardResponse{
			Status: constant.Success,
		}
		cardAuditLogService.On("SendActivityLog", mock.Anything, createVirtualCardCreationFailedEvent()).Once()
		service.publishCardCreationLog(ctx, req, res, api.DefaultInternalServerError)
		cardAuditLogService.AssertNumberOfCalls(t, "SendActivityLog", 1)
	})
	t.Run("should log virtual card creation success", func(t *testing.T) {
		cardAuditLogService := &audit.MockCardActivityLogService{}
		service := &DigicardCoreService{
			CardActivityLog: cardAuditLogService,
		}
		ctx := commonCtx.WithUserID(context.Background(), uuid.NewString())
		req := &api.CreateCardRequest{
			AccountID:      "AccountID",
			DisplayName:    "DisplayName",
			Type:           "PHYSICAL",
			IdempotencyKey: "IdempotencyKey",
			CardID:         "CardID",
			CardDesignId:   "CardDesignId",
			CnpEnabled:     false,
		}
		res := &api.CardResponse{
			Status: constant.Success,
		}
		cardAuditLogService.On("SendActivityLog", mock.Anything, createPhysicalCardCreationSuccessEvent()).Once()
		service.publishCardCreationLog(ctx, req, res, nil)
		cardAuditLogService.AssertNumberOfCalls(t, "SendActivityLog", 1)
	})
	t.Run("should log virtual card creation failed", func(t *testing.T) {
		cardAuditLogService := &audit.MockCardActivityLogService{}
		service := &DigicardCoreService{
			CardActivityLog: cardAuditLogService,
		}
		ctx := commonCtx.WithUserID(context.Background(), uuid.NewString())
		req := &api.CreateCardRequest{
			AccountID:      "AccountID",
			DisplayName:    "DisplayName",
			Type:           "PHYSICAL",
			IdempotencyKey: "IdempotencyKey",
			CardID:         "CardID",
			CardDesignId:   "CardDesignId",
			CnpEnabled:     false,
		}
		res := &api.CardResponse{
			Status: constant.Success,
		}
		cardAuditLogService.On("SendActivityLog", mock.Anything, createPhysicalCardCreationFailedEvent()).Once()
		service.publishCardCreationLog(ctx, req, res, api.DefaultInternalServerError)
		cardAuditLogService.AssertNumberOfCalls(t, "SendActivityLog", 1)
	})
}

func createVirtualCardCreationSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:  customerjournalapi.CardVirtualCreationEvent,
		Status: customerjournalapi.AuditEventSuccess,
		Source: customerjournalapi.TriggeredByGXBankAppSource,
	}
}

func createVirtualCardCreationFailedEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:        customerjournalapi.CardVirtualCreationEvent,
		Status:       customerjournalapi.AuditEventFailed,
		Source:       customerjournalapi.TriggeredByGXBankAppSource,
		FailedReason: api.DefaultInternalServerError.Error(),
	}
}

func createPhysicalCardCreationSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:  customerjournalapi.CardPhysicalCreationEvent,
		Status: customerjournalapi.AuditEventSuccess,
		Source: customerjournalapi.TriggeredByGXBankAppSource,
	}
}

func createPhysicalCardCreationFailedEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:        customerjournalapi.CardPhysicalCreationEvent,
		Status:       customerjournalapi.AuditEventFailed,
		Source:       customerjournalapi.TriggeredByGXBankAppSource,
		FailedReason: api.DefaultInternalServerError.Error(),
	}
}

func createCnpEnableSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:       customerjournalapi.CardOnboardOnlineTxnToggleEvent,
		Status:      customerjournalapi.AuditEventSuccess,
		Source:      customerjournalapi.TriggeredByGXBankAppSource,
		BeforeValue: map[string]interface{}{audit.CnpToggleDesc: constant.ToggleDisabled},
		AfterValue:  map[string]interface{}{audit.CnpToggleDesc: constant.ToggleEnabled},
	}
}

func createCnpDisableSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:       customerjournalapi.CardOnboardOnlineTxnToggleEvent,
		Status:      customerjournalapi.AuditEventSuccess,
		Source:      customerjournalapi.TriggeredByGXBankAppSource,
		BeforeValue: map[string]interface{}{audit.CnpToggleDesc: constant.ToggleDisabled},
		AfterValue:  map[string]interface{}{audit.CnpToggleDesc: constant.ToggleDisabled},
	}
}
