package storage

import "time"

// CardDesign ...
type CardDesign struct {
	ID             uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	ProductVariant string    `sql-col:"product_variant"`
	CardDesignID   string    `sql-col:"card_design_id"`
	Color          string    `sql-col:"color"`
	Description    string    `sql-col:"description"`
	FrontImage     string    `sql-col:"front_image"`
	BackImage      string    `sql-col:"back_image"`
	CreatedAt      time.Time `sql-col:"created_at"`
	UpdatedAt      time.Time `sql-col:"updated_at"`
	StockCount     uint64    `sql-col:"stock_count"`
	NameColor      string    `sql-col:"name_color"`
}

/*
const (
	logTagCardDesign = "storage.card design"
	ttlCardDesign    = 24 * 3600 // 1 day
)
*/
