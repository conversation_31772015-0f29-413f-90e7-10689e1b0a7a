package issuance

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestPersistUserCardMapping(t *testing.T) {
	errDummy := errors.New("simulate error")
	testcardID := uuid.NewString()
	testCardIssuanceType := "INDIVIDUAL"
	defaultCardStorage := []*storage.UserCardMapping{
		{
			InternalCardID: testcardID,
		},
	}
	defaultResponse := &dto.IssuanceResponse{
		DebitCardIssuanceResponse: &dto.DebitCardIssuanceResponse{
			AppHeader: nil,
			Response:  nil,
			CardInfoResponse: &dto.CardInfoResponse{
				CardNumber:    "",
				CardSeqNumber: 0,
				ProxyNumber:   "12345",
				ExpiryDate:    "",
			},
		},
	}
	jsonResponseBody, _ := json.Marshal(defaultResponse)
	defaultCardRequest := &api.CardIssuanceRequest{
		IdempotencyKey:   uuid.NewString(),
		CardIssuanceType: testCardIssuanceType,
		CardID:           testcardID,
		ProductVariant:   "test product variant",
	}
	scenarios := []struct {
		cardStorage            []*storage.UserCardMapping
		productVariant         string
		expectedState          workflowengine.State
		expectedProductVariant string
		errSave                error
		expectedErr            error
		description            string
	}{
		{
			cardStorage:   defaultCardStorage,
			errSave:       errDummy,
			expectedErr:   errDummy,
			expectedState: stVirtualCardIssued,
			description:   "db error",
		},
		{
			cardStorage:   defaultCardStorage,
			errSave:       nil,
			expectedState: stSuccess,
			description:   "success",
		},
		{
			cardStorage:            defaultCardStorage,
			productVariant:         "test product variant",
			errSave:                nil,
			expectedState:          stSuccess,
			expectedProductVariant: "test product variant",
			description:            "with product variant",
		},
	}

	for _, scenario := range scenarios {
		description := scenario.description
		mockStorage := &storage.MockIUserCardMappingDAO{}
		storage.UserCardMappingD = mockStorage
		w := WorkflowImpl{
			StatsD: statsd.NewNoop(),
		}

		t.Run(description, func(t *testing.T) {
			mockStorage.On("Save", mock.Anything, mock.MatchedBy(func(arg interface{}) bool {
				if entity, ok := arg.(*storage.UserCardMapping); ok {
					assert.Equal(t, scenario.expectedProductVariant, entity.ProductVariant, "ProductVariant not matched")
					assert.Equal(t, 1, entity.CardSequenceNumber, "CardSequence not matched")
					return true
				}
				return false
			})).Return(scenario.errSave).Once()

			resp, err := w.persistUserCardMapping(context.Background(), "", &ExecutionData{
				CardIssuanceRequest: defaultCardRequest,
				CardActivity: storage.CardActivity{
					ResponseBody: jsonResponseBody,
					ResponseCode: "00",
				},
				ProductVariant: scenario.productVariant,
			}, "")
			if scenario.expectedErr != nil {
				assert.Equal(t, err, scenario.expectedErr, "unexpected error")
				assert.Nil(t, resp, "unexpected response")
			} else {
				assert.NoError(t, err, "unexpected error")
				assert.NotNil(t, resp, "unexpected response")
				assert.Equal(t, scenario.expectedState, resp.GetState(), "unexpected state")
			}
			mockStorage.AssertExpectations(t)
		})
	}
}
