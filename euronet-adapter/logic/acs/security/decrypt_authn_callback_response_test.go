package security

import (
	"context"
	"crypto"
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common/signature"

	dtoACS "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
)

var (
	publicKey  = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi3Virx4HNxY9409f0JgIwZ7sMbuYYDXiCByO54hB2UyFpkYLr8cvcRL04sUDcyKC5TB9uDtgXpYjJTEAf80ywhUaVp02gjVb+u4rlqjrEO8+O102F8Cdf7T35Hs2c72HS0+2RHCfY+NUw+YUFyvjdRAh/PswDdYKh59Uwu/7H15rMYB41WlNBLe8zHPmLWazYwiXcFOyJvDaaIFvcPrXhziatPbagceveafXFPxCAsgIslg2k3NhKmKk59alSFrZXXM2xIAqSmG4BsS29BCd/mH/JMsF7caX+N66ul/OHJ8XfSt9zD2yvcC/l8z/pWeoeDjBemfjCo+O0zm7Y/h0owIDAQAB\n-----END PUBLIC KEY-----"
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
	authnKey   = "7299453b621d99c8276fe0e83fasfwty"
)

func Test_clientImpl_DecryptAuthnCallbackResponse(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *dtoACS.ACSCallbackResponse
	}
	tests := []struct {
		name    string
		args    args
		want    *dtoACS.ACSCallbackResponseDetails
		wantErr bool
	}{
		{
			name: "test save advice",
			args: args{
				ctx: context.Background(),
				request: &dtoACS.ACSCallbackResponse{
					EncryptedRequest: "sgcNHWLw9O3QpjZlW0BaJVutVe2Aw5mcpS92c/U/XJy1dpaGnvhUu0rHx0nWTBUrYzp34OI8Ig+C80ix28EPRJxbeyrIgI87W5gVRbjhrjRUlQjNYqwjcECR8DF0rIemMBPJAfeZ7WqwNTleRtrR8Hxm40DBkunp0r68/41zldvcZbsFRnWqSSce+/InoNC6eRPl5Zt1h/L3rIaidTPDwyIh8GcoOWrrxztnu9PVfjqbAtlSdiq3TzyCU2dz0RKXd6KBSL1EqirSuNU3JTzY/2hvA3xDnO1stADOUEekJ0HcQ8r5cyFq6ksiOuGlBnT8/0iC0HN0EJb3YAD+3Ic8joofu5qaDkF9M925d5oEU0GTSN3fNc0NfUoA+Jq2LhfodBAR+z5psm5xPO5rl6omMWa4MFbtliiq5w5RYBkRvruJ7QeyqV8w07nNFusy8w78ArNFyfdZmHAB+PR626WEDYfbtoP60hvu4orabzANyeRQ95MjCaUb758lF+OvAM7Ih2Z96C2oRGj8pYo5v9mAak1PVA9JcD+zja1oFBsp4gunE9rmpmiT9lXzI7bPWt4uLom4cS6Nl1/NWVJxtQNLYu8Tp8E7pcZ33FBa5+akCTN9fxlmDFgOa2BuwJ25S1mU",
				},
			},
			want: &dtoACS.ACSCallbackResponseDetails{
				BankTransactionID: "*************",
				ACSTransactionID:  "236547",
				ResponseCode:      "0000",
			},
		},
	}
	for _, tt := range tests {
		ctx := tt.args.ctx
		request := tt.args.request
		wantErr := tt.wantErr
		want := tt.want
		publicKeyAuth, err := signature.ParseRSAPublicKeyFromPEM([]byte(publicKey))
		require.NoError(t, err)
		privateKeyAuth, err := signature.ParseRSAPrivateKeyFromPEM([]byte(privateKey))
		require.NoError(t, err)

		mockClient := &clientImpl{
			SignatureClient:        signature.NewSigningMethodRSA("RS256", crypto.SHA256),
			authnReqRSAPublicKey:   publicKeyAuth,
			authnRespRSAPrivateKey: privateKeyAuth,
			authnAESKey:            authnKey,
		}

		t.Run(tt.name, func(t *testing.T) {
			got, err := mockClient.DecryptAuthnCallbackResponse(ctx, request)
			if (err != nil) != wantErr {
				t.Errorf("DecryptAuthnCallbackResponse() error = %v, wantErr %v", err, wantErr)
				return
			}
			if !reflect.DeepEqual(got, want) {
				t.Errorf("DecryptAuthnCallbackResponse() got = %v, want %v", got, want)
			}
		})
	}
}
