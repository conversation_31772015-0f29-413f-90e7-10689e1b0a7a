package storage

import "time"

// AdjustmentScenarioFailure ...
type AdjustmentScenarioFailure struct {
	ID                       uint64    `sql-col:"id" sql-key:"id" sql-insert:"false"`
	TransactionType          string    `sql-col:"transaction_type"`
	CardNumber               string    `sql-col:"card_number"`
	ProxyNumber              string    `sql-col:"proxy_number"`
	TxnAuditNumber           string    `sql-col:"txn_audit_number"`
	AuthCode                 string    `sql-col:"auth_code"`
	RetrievalReferenceNumber string    `sql-col:"retrieval_reference_number"`
	TxnCurrency              string    `sql-col:"txn_currency"`
	TxnAmount                int64     `sql-col:"txn_amount"`
	BillingCurrency          string    `sql-col:"billing_currency"`
	BillingAmount            int64     `sql-col:"billing_amount"`
	TxnDate                  string    `sql-col:"txn_date"`
	MerchantNameLoc          string    `sql-col:"merchant_name_loc"`
	JulianDate               string    `sql-col:"julian_date"`
	Status                   string    `sql-col:"status"`
	StatusReason             string    `sql-col:"status_reason"`
	InternalTransactionID    string    `sql-col:"internal_transaction_id"`
	OriginalRequestID        string    `sql-col:"original_request_id"`
	Source                   string    `sql-col:"source"`
	CreatedAt                time.Time `sql-col:"created_at"`
	UpdatedAt                time.Time `sql-col:"updated_at"`
}
