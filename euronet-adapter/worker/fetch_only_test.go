package worker

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
)

func TestFetchOnly(t *testing.T) {
	errDummy := errors.New("simulate error")

	scenarios := []struct {
		errLock     error
		expectedErr error
		errBlackout bool
		errExecute  error
	}{
		{
			errLock:     errDummy,
			expectedErr: nil,
		},
		{
			errBlackout: true,
			errLock:     nil,
			expectedErr: nil,
		},
		{
			errExecute:  errDummy,
			expectedErr: errDummy,
		},
		{
			errLock:     nil,
			expectedErr: nil,
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario
		mockRedis := &mocks.Client{}
		mockLock := &mocks.Lock{}
		mockWf := &file.MockWorkflow{}

		mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
			mockLock, testcase.errLock).Once()
		mockWf.On("ExecuteFileWorkflow", mock.Anything, mock.Anything).Return(testcase.errExecute)
		mockLock.On("Unlock", mock.Anything).Return(nil).Once()

		cache.RedisClient = mockRedis

		workerConfig := []config.FetchOnlyCfg{
			{
				FilePrefix: "asd",
				CronExpression: []string{
					"15 10 * * *",
				},
			},
		}

		w := FetchOnlyWorker{
			FetchOnlyCfg:           workerConfig,
			FileProcessingWorkflow: mockWf,
		}

		ctx := context.Background()
		err := w.executor(ctx, workerConfig[0])

		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
		} else {
			assert.NoError(t, err, description)
		}
	}
}
