package cancellation

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	customerMasterAPI "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/notification"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestNotify(t *testing.T) {
	errDummy := errors.New("simulate error")
	testcardID := uuid.NewString()
	metadata := &dto.RefundMetaData{
		TransactionCategory: string(constant.TransactionCategoryECOM),
	}
	refundMetaDataJSON, _ := dto.ConvertRefundMetaDataToJSON(metadata)
	defaultChargeStorage := &storage.Charge{
		CardID: testcardID,
	}
	defaultRefundStorage := storage.Refund{
		CardID: testcardID,
	}
	defaultRefundStorage.Metadata = &refundMetaDataJSON
	scenarios := []struct {
		currState     workflowengine.State
		chargeStorage *storage.Charge
		RefundStorage storage.Refund
		expectedState workflowengine.State
		notifyErr     error
		expectedErr   error
	}{
		{
			chargeStorage: defaultChargeStorage,
			RefundStorage: defaultRefundStorage,
			notifyErr:     errDummy,
			expectedErr:   errDummy,
		},
		{
			currState:     stCompletedPublished,
			expectedState: stCompletedNotified,
			chargeStorage: defaultChargeStorage,
			RefundStorage: defaultRefundStorage,
		},
		{
			currState:     stFailurePublished,
			expectedState: stFailureNotified,
			chargeStorage: defaultChargeStorage,
			RefundStorage: defaultRefundStorage,
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		mockNotifier := &notification.MockTransactionNotifier{}
		mockNotifier.On("Notify", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(testcase.notifyErr)
		w := WorkflowImpl{
			Notifier: mockNotifier,
			StatsD:   statsd.NewNoop(),
		}
		resp, err := w.notify(context.Background(), "", &ExecutionData{
			State:  testcase.currState,
			Refund: testcase.RefundStorage,
			Charge: testcase.chargeStorage,
			CreateRefund: &dto.CreateRefund{
				IdempotencyKey: uuid.NewString(),
			},
		}, "")
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
			assert.Equal(t, testcase.expectedState, resp.GetState(), description)
		}
	}
}

func TestSendEmail(t *testing.T) {
	errDummy := errors.New("simulate error")
	testcardID := uuid.NewString()
	testCustomerName := "dummyName"
	metadata := &dto.RefundMetaData{
		TransactionCategory: string(constant.TransactionCategoryECOM),
	}
	refundMetaDataJSON, _ := dto.ConvertRefundMetaDataToJSON(metadata)
	defaultChargeStorage := &storage.Charge{
		CardID: testcardID,
	}
	defaultRefundStorage := storage.Refund{
		CardID: testcardID,
	}
	mockCustomerData := &customerMasterAPI.Customer{
		PreferredName: &testCustomerName,
		Name:          &testCustomerName,
	}
	defaultRefundStorage.Metadata = &refundMetaDataJSON
	scenarios := []struct {
		desc                string
		transactionCategory string
		currState           workflowengine.State
		expectedState       workflowengine.State
		refundStorage       storage.Refund
		chargeStorage       *storage.Charge
		customerData        *customerMasterAPI.Customer
		sendEmailErr        error
		expectedErr         error
	}{
		{
			desc:          "happy path",
			currState:     stCompletedNotified,
			expectedState: stCompletedSendEmail,
			customerData:  mockCustomerData,
			chargeStorage: defaultChargeStorage,
			refundStorage: defaultRefundStorage,
		},
		{
			desc:                "clearing file - skip notify",
			currState:           stCompletedNotified,
			expectedState:       stCompletedSendEmail,
			transactionCategory: "CLEARING",
		},
		{
			desc:          "notifier error",
			customerData:  mockCustomerData,
			chargeStorage: defaultChargeStorage,
			refundStorage: defaultRefundStorage,
			sendEmailErr:  errDummy,
			expectedErr:   errDummy,
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario

		customerBytes, _ := json.Marshal(scenario.customerData)
		testGetCustomerResponse := &customerMasterAPI.GetCustomerResponse{Customer: &customerMasterAPI.CustomerData{Data: customerBytes}}
		mockCustomerMaster := &customerMasterMock.CustomerMaster{}
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(testGetCustomerResponse, nil).Once()

		mockEmailNotifier := &notification.MockTransactionNotifier{}
		mockEmailNotifier.On("NotifyV2", mock.Anything, mock.Anything, mock.Anything).Return(testcase.sendEmailErr)
		w := WorkflowImpl{
			EmailNotifier:        mockEmailNotifier,
			CustomerMasterClient: mockCustomerMaster,
			StatsD:               statsd.NewNoop(),
		}
		resp, err := w.sendEmail(context.Background(), "", &ExecutionData{
			State:  testcase.currState,
			Refund: testcase.refundStorage,
			Charge: testcase.chargeStorage,
			CreateRefund: &dto.CreateRefund{
				IdempotencyKey: uuid.NewString(),
				MetaData: dto.RefundMetaData{
					TransactionCategory: testcase.transactionCategory,
				},
			},
		}, "")
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
			assert.Equal(t, testcase.expectedState, resp.GetState(), description)
		}
	}
}
