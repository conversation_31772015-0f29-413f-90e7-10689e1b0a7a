package refund

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	digicardcoreAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/notification"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/server/config"
	customerMasterAPI "gitlab.myteksi.net/dakota/customer-master/api/v2"
	paymentCoreAPI "gitlab.myteksi.net/dakota/payment/payment-core/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"
)

const (
	logRefund        = "refund"
	workflowID       = "workflow_refund"
	refundLatencyTag = "refund_latency"
)

// WorkflowImpl is the implementation of the workflow
type WorkflowImpl struct {
	PaymentCoreClient    paymentCoreAPI.PaymentCore       `inject:"client.paymentCore"`
	Notifier             notification.TransactionNotifier `inject:"notifier.transaction"`
	Publisher            transaction.Publisher            `inject:"publisher.transaction"`
	StatsD               statsd.Client                    `inject:"statsD"`
	LockConf             *config.LockConfig               `inject:"config.redisLock"`
	DigicardCore         digicardcoreAPI.DigicardCore     `inject:"client.digicardCore"`
	VendorConfig         *config.VendorConfig             `inject:"config.vendorConfig"`
	CountryConfig        *config.CountryConfig            `inject:"config.countryConfig"`
	EmailNotifier        notification.TransactionNotifier `inject:"notifier.emailNotifier"`
	CustomerMasterClient customerMasterAPI.CustomerMaster `inject:"client.customerMaster"`
	WorkflowRetryConfig  *config.WorkflowRetryConfig      `inject:"config.workflowRetry"`
}

// states definition: https://wiki.grab.com/display/Digibank/Convention+and+Guideline
const (
	stInit                                   = we.StateInit
	stRefundPersisted                        = we.State(100)
	stProcessingPublished                    = we.State(101)
	stRefundWithoutOrigChargeIDPersisted     = we.State(102)
	stProcessingWithoutOrigChargeIDPublished = we.State(103)
	stRefunded                               = we.State(201)
	stRefundStreamPersisted                  = we.State(202)

	stFailed           = we.State(500)
	stFailurePublished = we.State(501)
	stFailureNotified  = we.State(502)
	stValidationFailed = we.State(503)

	stDisputeRefunded    = we.State(900)
	stRefundCompleted    = we.State(901)
	stCompletedPublished = we.State(902)
	stCompletedNotified  = we.State(903)
	stCompletedSendEmail = we.State(904)
	stCancelCompleted    = we.State(905)
)

// events definition
const (
	evNoNeed              = we.EventNoNeed
	evPersistRefund       = we.Event(101)
	evPersistRefundStream = we.Event(102)
)

// RegisterWorkflow should be called during initialisation to register individual workflow to the workflow engine
func (w *WorkflowImpl) RegisterWorkflow() {
	var notificationRetryOption *we.TransitionOptions
	if retryOption := w.WorkflowRetryConfig.Refund.RetryOption; retryOption != nil {
		notificationRetryOption = &we.TransitionOptions{RetryPolicy: &we.RetryPolicy{
			Interval:    retryOption.IntervalInSeconds,
			MaxAttempts: retryOption.MaxAttempt}}
	}
	refundWorkflow := we.NewWorkflow(workflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})

	refundWorkflow.AddTransition(logRefund, stInit, evPersistRefund, w.persistRefund, nil, stRefundPersisted, stRefundWithoutOrigChargeIDPersisted, stValidationFailed)
	refundWorkflow.AddTransition(logRefund, stRefundPersisted, evNoNeed, w.publish, nil, stProcessingPublished, stProcessingWithoutOrigChargeIDPublished)
	refundWorkflow.AddTransition(logRefund, stProcessingPublished, evNoNeed, w.refundTransfer, nil, stRefundCompleted, stRefunded, stFailed, stDisputeRefunded)
	refundWorkflow.AddTransition(logRefund, stRefunded, evPersistRefundStream, w.persistTransferStream, nil, stRefundStreamPersisted)
	refundWorkflow.AddTransition(logRefund, stRefundStreamPersisted, evNoNeed, w.completeTransfer, nil, stRefundCompleted, stFailed, stDisputeRefunded)
	refundWorkflow.AddTransition(logRefund, stDisputeRefunded, evNoNeed, w.disputeUpdate, nil, stRefundCompleted)

	// Success handling
	refundWorkflow.AddTransition(logRefund, stRefundCompleted, evNoNeed, w.publish, notificationRetryOption, stCompletedPublished)
	refundWorkflow.AddTransition(logRefund, stCancelCompleted, evNoNeed, w.publish, notificationRetryOption, stCompletedPublished)
	refundWorkflow.AddTransition(logRefund, stCompletedPublished, evNoNeed, w.notify, notificationRetryOption, stCompletedNotified)
	refundWorkflow.AddTransition(logRefund, stCompletedNotified, evNoNeed, w.sendEmail, notificationRetryOption, stCompletedSendEmail)

	// Failure handling
	refundWorkflow.AddTransition(logRefund, stFailed, evNoNeed, w.publish, notificationRetryOption, stFailurePublished)
	refundWorkflow.AddTransition(logRefund, stFailurePublished, evNoNeed, w.notify, notificationRetryOption, stFailureNotified)

	// Refund without original charge ID handling
	refundWorkflow.AddTransition(logRefund, stRefundWithoutOrigChargeIDPersisted, evNoNeed, w.getRefundInfo, nil, stRefundPersisted)
	refundWorkflow.AddTransition(logRefund, stProcessingWithoutOrigChargeIDPublished, evNoNeed, w.refundTransfer, nil, stRefundCompleted, stRefunded, stFailed)

	we.RegisterWorkflow(refundWorkflow)
}

// ConsumeFromPCStream provides the interface for async modules to resume workflow execution
func ConsumeFromPCStream(ctx context.Context, data *dto.StreamMessage) error {
	_, err := wfExecute(ctx, we.Execution{
		WorkflowID:     workflowID,
		TransitionID:   data.ReferenceID,
		ExecutionEvent: evPersistRefundStream,
	}, data)
	if err != nil {
		slog.FromContext(ctx).Warn(logRefund, "Error in executing workflow after consuming from payment core stream", slog.Error(err))
		return err
	}
	return nil
}

// Stats for tracking success and failure responses in refund workflow
func (w *WorkflowImpl) Stats(ctx context.Context, status string, statusReason string) {
	slog.FromContext(ctx).Debug(logRefund, fmt.Sprintf("publishing %s metric", status))
	w.StatsD.Count1(transaction.LogDigicardTransaction, logRefund,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}

// LatencyStats tracks how long it takes for a transaction to reach certain status
func (w *WorkflowImpl) LatencyStats(ctx context.Context, createdAt time.Time, status string) {
	slog.FromContext(ctx).Debug(refundLatencyTag, fmt.Sprintf("publishing %s lattency metric", status))
	w.StatsD.Duration(transaction.LogDigicardTransaction, refundLatencyTag, createdAt, fmt.Sprintf("status:%s", status))
}

func (w *WorkflowImpl) resolveUserName(ctx context.Context, safeID string) string {
	slog.FromContext(ctx).Debug(logRefund, "calling customer master - GetCustomer")
	customerObj, err := w.CustomerMasterClient.GetCustomer(ctx, &customerMasterAPI.GetCustomerRequest{
		ID: safeID,
		Target: &customerMasterAPI.TargetGroup{
			ServiceID: constant.ServiceID,
		},
	})
	if err != nil {
		slog.FromContext(ctx).Warn(logRefund, "CustomerMasterClient.GetCustomer error", slog.Error(err))
		return ""
	}
	customerData := &customerMasterAPI.Customer{}
	if err = json.Unmarshal(customerObj.Customer.Data, customerData); err != nil {
		return ""
	}

	// customer preferredName can be nil if not set
	preferredName := *customerData.Name
	if customerData.PreferredName != nil {
		preferredName = *customerData.PreferredName
	}

	return preferredName
}
